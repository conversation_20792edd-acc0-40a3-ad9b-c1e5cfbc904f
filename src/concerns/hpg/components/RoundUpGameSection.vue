<!-- GameSection.vue -->
<template>
  <div class="game-section">
    <!-- Game Title (if needed) -->
    <div v-if="game.game_title" class="game-section-title q-mb-lg">
      <h3 class="text-h4 text-weight-bold text-primary">
        {{ game.game_title }}
      </h3>
    </div>

    <!-- Properties Grid -->
    <div v-if="gameListings.length > 0" class="properties-grid">
      <div
        v-for="(listing, index) in gameListings"
        :key="listing.listing_details?.uuid || index"
        class="property-card-item"
      >
        <PropertyPreviewCardItem
          :listing="listing"
          :getPropertyMainImage="getPropertyMainImage"
          :getPropertyAddress="getPropertyAddress"
          @start="handleNavigate(listing)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import PropertyPreviewCardItem from 'src/concerns/realty-game/components/roundup/PropertyPreviewCardItem.vue'

// Props
const props = defineProps({
  game: {
    type: Object,
    required: true,
  },
  incomingGameListings: {
    type: Array,
    default: null,
  },
  getPropertyMainImage: {
    type: Function,
    required: true,
  },
  getPropertyAddress: {
    type: Function,
    required: true,
  },
})

// Computed property for game listings
const gameListings = computed(() => {
  return props.incomingGameListings !== null
    ? props.incomingGameListings
    : props.game.game_listings || []
})

// Emits
const emit = defineEmits(['navigate-to-property'])

// Methods
const handleNavigate = (listing) => {
  const gameGlobalSlug = props.game?.realty_game_details?.game_global_slug
  // console.log(props.game)
  emit('navigate-to-property', listing, gameGlobalSlug)
}
</script>

<style scoped>
/* Existing styles remain unchanged */
.game-section {
  width: 100%;
}

.game-section-title {
  text-align: center;
}

.properties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
  width: 100%;
}

.property-card-item {
  width: 100%;
  transition: all 0.3s ease;
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.property-card-item:nth-child(1) {
  animation-delay: 0.1s;
}
.property-card-item:nth-child(2) {
  animation-delay: 0.2s;
}
.property-card-item:nth-child(3) {
  animation-delay: 0.3s;
}
.property-card-item:nth-child(4) {
  animation-delay: 0.4s;
}
.property-card-item:nth-child(5) {
  animation-delay: 0.5s;
}
.property-card-item:nth-child(6) {
  animation-delay: 0.6s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced styling for property cards */
.property-card-item :deep(.property-card) {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  border: 3px solid transparent;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.property-card-item :deep(.property-card::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(37, 99, 235, 0.05),
    rgba(156, 39, 176, 0.05)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  z-index: 1;
}

.property-card-item :deep(.property-card:hover) {
  box-shadow: 0 25px 70px rgba(0, 0, 0, 0.25);
  transform: translateY(-12px) scale(1.02);
  border-color: #2563eb;
  background: linear-gradient(145deg, #ffffff, #fafbff);
}

.property-card-item :deep(.property-card:hover::before) {
  opacity: 1;
}

.property-card-item :deep(.property-image-container) {
  height: 320px;
  position: relative;
}

.property-card-item :deep(.property-image-container::after) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(37, 99, 235, 0.1) 0%,
    transparent 30%,
    transparent 70%,
    rgba(156, 39, 176, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.property-card-item
  :deep(.property-card:hover .property-image-container::after) {
  opacity: 1;
}

.property-card-item :deep(.guess-button) {
  border-radius: 12px;
  padding: 12px 24px;
  font-weight: 700;
  font-size: 1rem;
  background: linear-gradient(135deg, #2563eb, #3b82f6, #1d4ed8);
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
  box-shadow: 0 6px 20px rgba(37, 99, 235, 0.3);
  transition: all 0.3s ease;
}

.property-card-item :deep(.guess-button:hover) {
  background: linear-gradient(135deg, #1d4ed8, #2563eb, #1e40af);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
  transform: translateY(-2px);
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .properties-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 28px;
  }
}

@media (max-width: 768px) {
  .properties-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
  }
  .property-card-item :deep(.property-image-container) {
    height: 280px;
  }
}

@media (max-width: 480px) {
  .properties-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  .property-card-item :deep(.property-image-container) {
    height: 260px;
  }
  .property-card-item :deep(.guess-button) {
    font-size: 0.9rem;
    padding: 10px 20px;
  }
}
</style>
