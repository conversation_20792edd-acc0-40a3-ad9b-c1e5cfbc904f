<!-- RoundUpGamesDisplay.vue -->
<template>
  <div class="round-up-games-display">
    <!-- No Games State -->
    <div v-if="!roundUpRealtyGames.length"
         class="text-center q-pa-xl text-grey-6">
      <q-icon name="home"
              size="4em"
              class="q-mb-md" />
      <div class="text-h6">No properties available</div>
      <div class="text-body2">Check back later for new properties</div>
    </div>
    <!-- Games List -->
    <div v-else class="games-list">
      <RoundUpGameSection
        v-for="game in roundUpRealtyGames"
        :key="game.id"
        :game="game"
        :getPropertyMainImage="getPropertyMainImage"
        :getPropertyAddress="getPropertyAddress"
        @navigate-to-property="handleNavigateToProperty"
      />
    </div>
  </div>
</template>

<script setup>
// import NoGamesState from './NoGamesState.vue'
// import GameSection from './GameSection.vue'
import RoundUpGameSection from 'src/concerns/hpg/components/RoundUpGameSection.vue'

// Props
const props = defineProps({
  roundUpRealtyGames: {
    type: Array,
    default: () => []
  },
  getPropertyMainImage: {
    type: Function,
    required: true
  },
  getPropertyAddress: {
    type: Function,
    required: true
  }
})

// Emits
const emit = defineEmits(['navigate-to-property'])

// Methods
const handleNavigateToProperty = (listing, gameGlobalSlug) => {
  // console.log(props.roundUpRealtyGames)
  emit('navigate-to-property', listing, gameGlobalSlug)
}
</script>

<style scoped>
.round-up-games-display {
  width: 100%;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 48px; /* Spacing between game sections */
}

@media (max-width: 768px) {
  .games-list {
    gap: 32px;
  }
}
</style>