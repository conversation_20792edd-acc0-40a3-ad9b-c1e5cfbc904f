# Property Data Structure Documentation

## Overview

The RoundupGamePagesLayout component provides a unified interface for accessing both game listing data and sale listing data through the `currentPropertyListing` computed property. This document explains the data structure and how to access different types of property information.

## API Response Structure

The `/api_public/v4/game_sale_listings/show_rgl/{uuid}` endpoint returns data in the following format:

```json
{
  "realty_game_listing": {
    "uuid": "game-listing-uuid",
    "gl_title_atr": "4 bedroom character property for sale",
    "gl_vicinity_atr": "Cossington, England",
    "gl_image_url_atr": "https://example.com/image.jpg",
    "gl_country_code_atr": "",
    "gl_description_atr": "",
    "visible_in_game": true,
    "position_in_game": 0
  },
  "sale_listing": {
    "uuid": "sale-listing-uuid",
    "title": "4 bedroom character property for sale",
    "street_address": "Main Street, Cossington, Leicestershire",
    "city": "Cossington",
    "postal_code": "LE7 4UW",
    "country": "England",
    "price_sale_current_cents": 55000000,
    "currency": "GBP",
    "count_bedrooms": 4,
    "count_bathrooms": 2,
    "count_garages": 1,
    "description": "Property description...",
    "latitude": 52.714286,
    "longitude": -1.103609,
    "sale_listing_pics": [
      {
        "flag_is_hidden": false,
        "image_details": {
          "url": "https://example.com/image.jpg"
        }
      }
    ]
  }
}
```

## currentPropertyListing Structure

The `currentPropertyListing` computed property transforms the API response into a clean, accessible structure:

```javascript
{
  realty_game_listing: {
    // Game-specific properties
    gl_title_atr: "4 bedroom character property for sale",
    gl_vicinity_atr: "Cossington, England",
    gl_image_url_atr: "https://example.com/image.jpg",
    // ... other game listing properties
  },
  sale_listing: {
    // Property details
    title: "4 bedroom character property for sale",
    street_address: "Main Street, Cossington, Leicestershire",
    city: "Cossington",
    postal_code: "LE7 4UW",
    price_sale_current_cents: 55000000,
    sale_listing_pics: [...],
    // ... other sale listing properties
  }
}
```

## Usage Examples

### Accessing Game Listing Data

Use the `realty_game_listing` nested object for game-specific properties:

```vue
<template>
  <!-- Game title -->
  <h1>{{ currentPropertyListing.realty_game_listing.gl_title_atr }}</h1>
  
  <!-- Game vicinity -->
  <p>{{ currentPropertyListing.realty_game_listing.gl_vicinity_atr }}</p>
  
  <!-- Game image -->
  <img :src="currentPropertyListing.realty_game_listing.gl_image_url_atr" />
</template>
```

### Accessing Sale Listing Data

Use the `sale_listing` nested object for property details:

```vue
<template>
  <!-- Property address -->
  <p v-if="currentPropertyListing.sale_listing.street_address">
    {{ currentPropertyListing.sale_listing.street_address }}
  </p>
  
  <!-- City and postal code -->
  <p v-else>
    {{ currentPropertyListing.sale_listing.city }},
    {{ currentPropertyListing.sale_listing.postal_code }}
  </p>
  
  <!-- Property features -->
  <div v-if="currentPropertyListing.sale_listing.count_bedrooms">
    {{ currentPropertyListing.sale_listing.count_bedrooms }} Bedrooms
  </div>
  
  <!-- Property description -->
  <div v-if="currentPropertyListing.sale_listing.description">
    {{ currentPropertyListing.sale_listing.description }}
  </div>
</template>
```

### Accessing Property Images

Property images are available through the `sale_listing.sale_listing_pics` array:

```vue
<template>
  <div v-for="image in visibleImages" :key="image.uuid">
    <img :src="image.image_details.url" />
  </div>
</template>

<script>
const visibleImages = computed(() => {
  if (!currentPropertyListing.value?.sale_listing?.sale_listing_pics) return []
  return currentPropertyListing.value.sale_listing.sale_listing_pics.filter(
    image => !image.flag_is_hidden
  )
})
</script>
```

## Component Integration

### RoundupGamePropertyPage.vue

The RoundupGamePropertyPage component receives the `currentPropertyListing` prop and creates a backward-compatible `currentProperty` computed property:

```javascript
const currentProperty = computed(() => {
  if (props.currentPropertyListing) {
    // Merge both objects for direct property access
    const gameData = props.currentPropertyListing.realty_game_listing || {}
    const saleData = props.currentPropertyListing.sale_listing || {}
    return {
      ...gameData,
      ...saleData,
      // Keep nested structure available too
      realty_game_listing: gameData,
      sale_listing: saleData
    }
  }
  // ... other fallback logic
})
```

This allows existing template code to continue working while providing access to the new nested structure.

## Data Flow

1. **API Call**: `fetchPropertyData()` calls the API endpoint
2. **Raw Storage**: Response stored in `rawPropertyData` ref
3. **Computed Transform**: `currentPropertyListing` computed property creates clean structure
4. **Prop Passing**: Layout passes `currentPropertyListing` to child components
5. **Component Usage**: Child components access data via nested structure

## Error Handling

The system gracefully handles missing data:

```javascript
// Safe access patterns
const gameTitle = currentPropertyListing.value?.realty_game_listing?.gl_title_atr || 'Default Title'
const address = currentPropertyListing.value?.sale_listing?.street_address || ''
const images = currentPropertyListing.value?.sale_listing?.sale_listing_pics || []
```

## Migration Notes

### From Old Structure

If migrating from direct property access, update your templates:

```vue
<!-- OLD -->
<h1>{{ currentPropertyData.gl_title_atr }}</h1>
<p>{{ currentPropertyData.street_address }}</p>

<!-- NEW -->
<h1>{{ currentPropertyListing.realty_game_listing.gl_title_atr }}</h1>
<p>{{ currentPropertyListing.sale_listing.street_address }}</p>
```

### Backward Compatibility

The RoundupGamePropertyPage maintains backward compatibility by merging the nested objects into a flat structure in its `currentProperty` computed property. This allows existing code to continue working without changes.

## Best Practices

1. **Use Nested Access**: Always access properties through their appropriate nested object (`realty_game_listing` or `sale_listing`)
2. **Safe Navigation**: Use optional chaining (`?.`) to handle missing data
3. **Type Checking**: Check for data existence before rendering
4. **Consistent Naming**: Use the full nested path for clarity in templates

## Testing

The structure includes comprehensive tests covering:
- Proper nested structure creation
- Image filtering functionality
- Graceful handling of missing data
- Partial data structure handling
- Prop passing to child components
- Backward compatibility in child components

See the following test files for complete examples:
- `src/concerns/realty-game/tests/RoundupGamePagesLayout.test.js`
- `src/concerns/realty-game/tests/RoundupGamePropertyPage.test.js`

## Troubleshooting

### Common Issues

1. **Property not displaying**: Check that you're accessing the correct nested object
   ```javascript
   // Wrong
   currentPropertyListing.gl_title_atr

   // Correct
   currentPropertyListing.realty_game_listing.gl_title_atr
   ```

2. **Images not showing**: Ensure you're filtering hidden images
   ```javascript
   const visibleImages = images.filter(img => !img.flag_is_hidden)
   ```

3. **Store data conflicts**: If using the Pinia store, ensure the data structure matches expectations

### Debug Tips

1. **Console logging**: Log the entire structure to understand the data
   ```javascript
   console.log('currentPropertyListing:', currentPropertyListing.value)
   ```

2. **Template debugging**: Use JSON display in templates
   ```vue
   <pre>{{ JSON.stringify(currentPropertyListing, null, 2) }}</pre>
   ```

3. **Check API response**: Verify the API returns the expected structure
   ```javascript
   console.log('API response:', response.data)
   ```
