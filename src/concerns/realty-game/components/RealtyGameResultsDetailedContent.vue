<template>
  <div class="rg-results-detailed-content">
    <!-- Results Header -->
    <div class="results-header q-mb-xl text-center">

      <h1 class="text-h4 text-weight-bold text-primary q-mb-lg">
        {{ isCurrentUserSession ? `Well done ${ssGameSession.game_player_nickname}, challenge complete` :
          `${ssGameSession.game_player_nickname}'s challenge results` }}
      </h1>

      <!-- Session Info and Overall Ranking -->
      <div class="session-info q-mb-md">
        <template v-if="overallRanking">
          <q-chip v-if="overallRanking.rank === 1"
                  color="amber-7"
                  text-color="black"
                  icon="military_tech"
                  size="lg"
                  class="q-mb-sm text-h6 text-weight-bold shadow-2"
                  style="font-size: 1.2em; padding: 0.7em 1.5em">
            🥇 {{ isCurrentUserSession ? 'Congratulations! You are the' : `${ssGameSession.game_player_nickname} is
            the` }}
            <span class="text-weight-bold">&nbsp;Top Player</span> &nbsp;(1st of
            {{ overallRanking.total_sessions }})
          </q-chip>
          <q-chip v-else-if="overallRanking.rank === 2"
                  color="blue-grey-2"
                  text-color="black"
                  icon="military_tech"
                  size="lg"
                  class="q-mb-sm text-h6 text-weight-bold shadow-2"
                  style="font-size: 1.2em; padding: 0.7em 1.5em">
            🥈 {{ isCurrentUserSession ? 'Amazing! You ranked' : `${ssGameSession.game_player_nickname} ranked` }}
            <span class="text-weight-bold">&nbsp;2nd&nbsp;</span> of
            {{ overallRanking.total_sessions }}
          </q-chip>
          <q-chip v-else
                  color="info"
                  text-color="white"
                  icon="emoji_events">
            {{ isCurrentUserSession ? 'You ranked' : `${ssGameSession.game_player_nickname} ranked` }} {{
              overallRanking.rank }} of
            {{ overallRanking.total_sessions }}
          </q-chip>
        </template>
      </div>


      <div class="performance-badge q-mb-md">
        <q-icon :name="playerResults.performance_rating?.icon"
                :color="playerResults.performance_rating?.color"
                size="4em" />
        <div class="text-h4 text-weight-bold q-mt-md"
             :class="`text-${playerResults.performance_rating?.color}`">
          {{ playerResults.performance_rating?.rating }}
        </div>
        <div class="text-h6 text-grey-7">
          {{ playerResults.total_score }} /
          {{ playerResults.max_possible_score }} points
        </div>
      </div>

      <div>
        <SocialSharing socialSharingPrompt="Challenge your friends and see how they compare!
              Share your results without revealing the actual property prices."
                       socialSharingTitle="Check out how I did in this property price game"
                       :urlProp="shareableResultsUrl"></SocialSharing>

        <div>
          <a :href="shareableResultsUrl">{{ shareableResultsUrl }}</a>
        </div>
      </div>
      <p class="text-body1 text-grey-7">
        <!-- Here's how you performed on each property -->
      </p>
    </div>

    <!-- Leaderboard Section -->
    <q-card v-if="showLeaderboard && leaderboard.length > 0"
            class="leaderboard-card q-mb-lg"
            flat
            bordered>
      <q-card-section>
        <div class="text-h6 q-mb-md">
          <q-icon name="leaderboard"
                  color="primary"
                  size="sm"
                  class="q-mr-sm" />
          Leaderboard
        </div>
        <q-table :rows="leaderboard"
                 :columns="leaderboardColumns"
                 row-key="uuid"
                 flat
                 :pagination="{ rowsPerPage: 0 }"
                 :hide-bottom="leaderboard.length < 10"
                 class="leaderboard-table">
          <template v-slot:body-cell-performance_percentage="props">
            <q-td :props="props">
              {{ props.row.performance_percentage }}%
            </q-td>
          </template>
          <template v-slot:body-cell-created_at="props">
            <q-td :props="props">
              {{ new Date(props.row.created_at).toLocaleDateString() }}
            </q-td>
          </template>
        </q-table>
      </q-card-section>
    </q-card>

    <!-- Results Table -->
    <q-card class="results-card q-mb-lg"
            flat
            bordered>
      <q-card-section class="q-pa-lg">
        <div class="text-h6 q-mb-md">
          <q-icon name="leaderboard"
                  color="primary"
                  size="sm"
                  class="q-mr-sm" />
          {{ isCurrentUserSession ? 'How you performed on each property' : `How
          ${ssGameSession.game_player_nickname}
          performed on each property` }}
        </div>
        <!-- Note: :rows is now bound to gameBreakdown -->
        <q-table :rows="gameBreakdown"
                 :columns="resultsColumns"
                 row-key="uuid"
                 flat
                 :pagination="{ rowsPerPage: 0 }"
                 :hide-bottom="gameBreakdown.length < 10"
                 class="results-table">
          <!-- The v-slot templates remain largely the same because the `game_results` array structure is the same -->
          <template v-slot:body-cell-property="props">
            <q-td :props="props">
              <div class="property-cell"
                   style="overflow: auto">
                <router-link :to="{
                  name: 'rPriceGameProperty',
                  params: { listingInGameUuid: props.row.listing_uuid },
                }">
                  <div class="text-weight-medium">
                    {{ props.row.estimate_title }}
                  </div>
                </router-link>
                <div class="text-caption text-grey-6">
                  {{ props.row.estimate_vicinity }}
                </div>
              </div>
            </q-td>
          </template>

          <template v-if="isCurrentUserSession"
                    v-slot:body-cell-guess="props">
            <q-td :props="props">
              <div class="text-weight-medium">
                {{
                  formatPriceWithBothCurrencies(
                    props.row.guessed_price_in_ui_currency_cents,
                    props.row.ui_currency,
                    true,
                    props.row.source_listing_currency
                  )
                }}
              </div>
            </q-td>
          </template>

          <template v-if="isCurrentUserSession"
                    v-slot:body-cell-actual="props">
            <q-td :props="props">
              <div v-if="props.row.source_listing_currency"
                   class="text-weight-medium">
                {{
                  formatPriceWithBothCurrencies(
                    props.row.price_at_time_of_estimate_cents,
                    props.row.source_listing_currency,
                    false
                  )
                }}
              </div>
            </q-td>
          </template>

          <template v-slot:body-cell-difference="props">
            <q-td :props="props">
              <q-chip :color="getScoreColor(props.row.score_for_guess || 0)"
                      text-color="white"
                      dense
                      size="md">
                {{ props.row.percentage_above_or_below > 0 ? '+' : ''
                }}{{ props.row.percentage_above_or_below?.toFixed(1) }}%
              </q-chip>
            </q-td>
          </template>

          <template v-slot:body-cell-score="props">
            <q-td :props="props">
              <div class="score-cell">
                <q-chip :color="getScoreColor(props.row.score_for_guess || 0)"
                        text-color="white"
                        dense
                        size="md"
                        class="q-pa-sm">
                  {{ props.row.score_for_guess || 0 }}
                </q-chip>
              </div>
            </q-td>
          </template>
        </q-table>
      </q-card-section>
    </q-card>

    <!-- Comparison Summary -->
    <q-card v-if="comparisonSummary.length > 0"
            class="comparison-card q-mb-lg"
            flat
            bordered>
      <q-card-section class="q-pa-lg">
        <div class="text-h6 q-mb-md">
          <q-icon name="people"
                  color="primary"
                  size="sm"
                  class="q-mr-sm" />
          {{ isCurrentUserSession ? 'How You Compare to Other Players' : `How ${ssGameSession.game_player_nickname}
          Compares
          to Other Players` }}
        </div>
        <div>
          <div v-for="(propertyData, index) in comparisonSummary"
               :key="index"
               class="property-comparison q-mb-lg">
            <div class="comparison-header q-mb-md">
              <div v-if="propertyData.property_url"
                   class="text-subtitle1 text-weight-medium">
                <a :href="propertyData.property_url">{{
                  propertyData.property_title
                }}</a>
              </div>
              <div v-else
                   class="text-subtitle1 text-weight-medium">
                {{ propertyData.property_title }}
              </div>
              <div class="text-caption text-grey-6">
                {{ propertyData.property_vicinity }}
              </div>
            </div>

            <!-- Your guess vs others - only show prices for current user -->
            <div v-if="isCurrentUserSession"
                 class="comparison-stats q-mb-md">
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-4">
                  <q-card flat
                          bordered
                          class="stat-card">
                    <q-card-section class="text-center q-pa-md">
                      <div class="text-h6 text-primary">
                        {{ propertyData.your_guess_formatted }}
                      </div>
                      <div class="text-caption text-grey-6">Your Guess</div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-md-4">
                  <q-card flat
                          bordered
                          class="stat-card">
                    <q-card-section class="text-center q-pa-md">
                      <div class="text-h6 text-secondary">
                        {{ propertyData.average_guess_formatted }}
                      </div>
                      <div class="text-caption text-grey-6">
                        Average Guess
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
                <div class="col-12 col-md-4">
                  <q-card flat
                          bordered
                          class="stat-card">
                    <q-card-section class="text-center q-pa-md">
                      <div class="text-h6 text-positive">
                        {{ propertyData.actual_price_formatted }}
                      </div>
                      <div class="text-caption text-grey-6">
                        Actual Price
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>

            <!-- Performance ranking -->
            <div class="performance-ranking">
              <div class="text-subtitle2 q-mb-sm">{{ isCurrentUserSession ? 'Your Performance' :
                `${ssGameSession.game_player_nickname}'s Performance` }}</div>
              <div class="ranking-info">
                <q-chip :color="propertyData.ranking.color"
                        text-color="white"
                        icon="emoji_events">
                  Ranked {{ propertyData.ranking.rank }} of
                  {{ propertyData.ranking.total_players }}
                </q-chip>
                <span class="q-ml-md text-body2 text-grey-7">{{
                  propertyData.ranking.performance_text
                }}</span>
              </div>
            </div>
            <q-separator v-if="index < comparisonSummary.length - 1"
                         class="q-mt-lg" />
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Community Details Section -->
    <q-no-ssr>
      <div v-if="gameCommunitiesDetailsCalc.show"
           class="results-actions q-pa-md text-center bg-grey-2 rounded-borders shadow-2">
        <q-card-section class="text-center">
          <div class="text-h6 q-mb-sm">🎮 Join the Community Discussion</div>
          <q-btn :label="gameCommunitiesDetailsCalc.redditCommunity.url"
                 :href="gameCommunitiesDetailsCalc.redditCommunity.url"
                 type="a"
                 color="red"
                 target="_blank"
                 icon="mdi-reddit"
                 flat />
        </q-card-section>

        <q-card-section>
          <div class="text-subtitle1 q-mb-sm text-center">
            🧠 Other Price Guess Games You Might Like
          </div>
          <q-list bordered
                  separator
                  class="rounded-borders">
            <q-item v-for="(
relevantGame, index
              ) in gameCommunitiesDetailsCalc.relevantGames"
                    :key="index"
                    clickable>
              <q-item-section>
                <a :href="`https://${relevantGame}`"
                   target="_blank"
                   class="text-primary">
                  {{ `https://${relevantGame}` }}
                </a>
              </q-item-section>
            </q-item>
          </q-list>
        </q-card-section>
      </div>
    </q-no-ssr>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import SocialSharing from 'src/concerns/dossiers/components/sharing/SocialSharing.vue'

const props = defineProps({
  results: {
    type: Object,
    required: true
  },
  playerResults: {
    type: Object,
    required: true
  },
  ssGameSession: {
    type: Object,
    required: true
  },
  overallRanking: {
    type: Object,
    default: null
  },
  leaderboard: {
    type: Array,
    default: () => []
  },
  gameBreakdown: {
    type: Array,
    required: true
  },
  comparisonSummary: {
    type: Array,
    required: true
  },
  isCurrentUserSession: {
    type: Boolean,
    required: true
  },
  showLeaderboard: {
    type: Boolean,
    default: false
  },
  formatPriceWithBothCurrencies: {
    type: Function,
    required: true
  },
  getScoreColor: {
    type: Function,
    required: true
  },
  gameCommunitiesDetailsCalc: {
    type: Object,
    required: true
  },
  shareableResultsUrl: {
    type: String,
    required: true
  }
})

// Leaderboard columns
const leaderboardColumns = [
  {
    name: 'session_guest_name',
    label: 'Player',
    field: 'session_guest_name',
    align: 'left',
  },
  { name: 'total_score', label: 'Score', field: 'total_score', align: 'right' },
  {
    name: 'max_possible_score',
    label: 'Max Score',
    field: 'max_possible_score',
    align: 'right',
  },
  {
    name: 'performance_percentage',
    label: '%',
    field: 'performance_percentage',
    align: 'right',
  },
  { name: 'created_at', label: 'Date', field: 'created_at', align: 'right' },
]



const resultsColumns = computed(() => {
  const columns = [
    {
      name: 'property',
      label: 'Property',
      field: 'property',
      align: 'left',
      style: 'width: 30%',
    },
  ]

  // Only show price columns for current user's session
  if (props.isCurrentUserSession) {
    columns.push(
      {
        name: 'guess',
        label: 'Your Guess',
        field: 'guess',
        align: 'right',
        style: 'width: 20%',
      },
      {
        name: 'actual',
        label: 'Actual Price',
        field: 'actual',
        align: 'right',
        style: 'width: 20%',
      }
    )
  }

  // Always show difference and score
  columns.push(
    {
      name: 'difference',
      label: 'Difference',
      field: 'difference',
      align: 'center',
      style: props.isCurrentUserSession ? 'width: 15%' : 'width: 35%',
    },
    {
      name: 'score',
      label: 'Score',
      field: 'score',
      align: 'center',
      style: props.isCurrentUserSession ? 'width: 15%' : 'width: 35%',
    }
  )

  return columns
})
</script>

<style scoped>
.results-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-badge {
  display: inline-block;
  padding: 2rem;
  border-radius: 16px;
  border: 2px solid #e9ecef;
}

.session-info {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.results-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.results-table {
  border-radius: 8px;
}

@media (max-width: 768px) {
  .property-cell {
    max-width: 200px;
  }
}

.score-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.comparison-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.stat-card {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.performance-ranking {
  border-radius: 8px;
  padding: 1rem;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.results-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .results-header {
    padding: 1rem;
  }

  .session-info {
    flex-direction: column;
    align-items: center;
  }

  .comparison-stats .row {
    flex-direction: column;
  }

  .ranking-info {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
