<template>
  <div v-if="playerResults && Object.keys(playerResults).length > 0" class="performance-badge q-mb-md text-center">
    <q-icon :name="playerResults.performance_rating?.icon"
            :color="playerResults.performance_rating?.color"
            size="4em" />
    <div class="text-h4 text-weight-bold q-mt-md"
         :class="`text-${playerResults.performance_rating?.color}`">
      {{ playerResults.performance_rating?.rating }}
    </div>
    <div class="text-h6 text-grey-7">
      {{ playerResults.total_score || 0 }} /
      {{ playerResults.max_possible_score || 0 }} points
    </div>
  </div>
  <div v-else class="performance-badge q-mb-md text-center">
    <q-spinner-dots color="primary" size="4em" />
    <div class="text-h6 text-grey-7 q-mt-md">Loading results...</div>
  </div>
</template>

<script setup>
const props = defineProps({
  playerResults: {
    type: Object,
    required: false,
    default: () => ({})
  }
})
</script>

<style scoped>
.performance-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
