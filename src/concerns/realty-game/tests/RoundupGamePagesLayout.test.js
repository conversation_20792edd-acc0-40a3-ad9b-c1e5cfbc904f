import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ref, computed } from 'vue'
import RoundupGamePagesLayout from '../layouts/RoundupGamePagesLayout.vue'

// Mock the router and route
const mockRoute = {
  name: 'rRoundupGameProperty',
  params: {
    gameSlug: 'test-game',
    listingInGameUuid: 'test-uuid'
  },
  query: {}
}

const mockRouter = {
  push: vi.fn(),
  resolve: vi.fn(() => ({ href: '/test-path' }))
}

// Mock composables
vi.mock('vue-router', () => ({
  useRoute: () => mockRoute,
  useRouter: () => mockRouter
}))

vi.mock('quasar', () => ({
  useQuasar: () => ({
    notify: vi.fn()
  })
}))

// Mock axios
vi.mock('axios', () => ({
  default: {
    get: vi.fn()
  }
}))

// Mock composables
vi.mock('src/concerns/realty-game/composables/useSingleListingRealtyGame', () => ({
  useSingleListingRealtyGame: () => ({
    totalProperties: ref(5),
    gameTitle: ref('Test Game'),
    realtyGameSummary: ref({}),
    gameCommunitiesDetails: ref({}),
    firstPropListing: ref({}),
    fetchPriceGuessData: vi.fn(),
    gameDefaultCurrency: ref('GBP'),
    setRealtyGameData: vi.fn()
  })
}))

vi.mock('src/concerns/realty-game/composables/useRealtyGameStorage', () => ({
  useRealtyGameStorage: () => ({
    getCurrentSessionId: vi.fn(() => 'test-session'),
    getCurrencySelection: vi.fn(),
    initializeFromStorage: vi.fn()
  })
}))

vi.mock('src/stores/realtyGame', () => ({
  useRealtyGameStore: () => ({
    isDataLoaded: false,
    gameListings: [],
    currentProperty: null
  })
}))

vi.mock('src/concerns/realty-game/composables/useServerSingleListingGameResults', () => ({
  useServerSingleListingGameResults: () => ({
    isLoading: ref(false),
    error: ref(null),
    results: ref(null),
    playerResults: ref({}),
    comparisonSummary: ref([]),
    gameBreakdown: ref([]),
    ssGameSession: ref({}),
    fetchResults: vi.fn(),
    getScoreColor: vi.fn(),
    realtyGameListingDetails: ref({})
  })
}))

vi.mock('src/concerns/realty-game/composables/useCurrencyConverter', () => ({
  useCurrencyConverter: () => ({
    setCurrency: vi.fn(),
    formatPriceWithBothCurrencies: vi.fn()
  })
}))

describe('RoundupGamePagesLayout', () => {
  let wrapper

  const mockApiResponse = {
    realty_game_listing: {
      uuid: 'game-listing-uuid',
      gl_title_atr: '4 bedroom character property for sale',
      gl_vicinity_atr: 'Cossington, England',
      gl_image_url_atr: 'https://example.com/image.jpg',
      visible_in_game: true
    },
    sale_listing: {
      uuid: 'sale-listing-uuid',
      title: '4 bedroom character property for sale',
      street_address: 'Main Street, Cossington, Leicestershire',
      city: 'Cossington',
      postal_code: 'LE7 4UW',
      country: 'England',
      price_sale_current_cents: 55000000,
      currency: 'GBP',
      count_bedrooms: 4,
      count_bathrooms: 2,
      sale_listing_pics: [
        {
          flag_is_hidden: false,
          image_details: {
            url: 'https://example.com/image1.jpg'
          }
        },
        {
          flag_is_hidden: true,
          image_details: {
            url: 'https://example.com/image2.jpg'
          }
        }
      ]
    }
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should create currentPropertyListing with proper nested structure', async () => {
    // Mock axios response
    const axios = await import('axios')
    axios.default.get.mockResolvedValue({ data: mockApiResponse })

    wrapper = mount(RoundupGamePagesLayout, {
      global: {
        stubs: ['router-view']
      }
    })

    // Set the raw property data to simulate API response
    wrapper.vm.rawPropertyData = mockApiResponse

    await wrapper.vm.$nextTick()

    const currentPropertyListing = wrapper.vm.currentPropertyListing

    // Test that the structure is correct
    expect(currentPropertyListing).toBeDefined()
    expect(currentPropertyListing.realty_game_listing).toBeDefined()
    expect(currentPropertyListing.sale_listing).toBeDefined()

    // Test game listing data access
    expect(currentPropertyListing.realty_game_listing.gl_title_atr).toBe('4 bedroom character property for sale')
    expect(currentPropertyListing.realty_game_listing.gl_vicinity_atr).toBe('Cossington, England')

    // Test sale listing data access
    expect(currentPropertyListing.sale_listing.street_address).toBe('Main Street, Cossington, Leicestershire')
    expect(currentPropertyListing.sale_listing.city).toBe('Cossington')
    expect(currentPropertyListing.sale_listing.count_bedrooms).toBe(4)
  })

  it('should filter property images correctly', async () => {
    wrapper = mount(RoundupGamePagesLayout, {
      global: {
        stubs: ['router-view']
      }
    })

    // Set the raw property data
    wrapper.vm.rawPropertyData = mockApiResponse

    await wrapper.vm.$nextTick()

    const propertyImages = wrapper.vm.propertyImages

    // Should only include non-hidden images
    expect(propertyImages).toHaveLength(1)
    expect(propertyImages[0].flag_is_hidden).toBe(false)
    expect(propertyImages[0].image_details.url).toBe('https://example.com/image1.jpg')
  })

  it('should handle missing data gracefully', async () => {
    wrapper = mount(RoundupGamePagesLayout, {
      global: {
        stubs: ['router-view']
      }
    })

    // Test with null data
    wrapper.vm.rawPropertyData = null

    await wrapper.vm.$nextTick()

    expect(wrapper.vm.currentPropertyListing).toBeNull()
    expect(wrapper.vm.propertyImages).toEqual([])
  })

  it('should handle partial data structures', async () => {
    wrapper = mount(RoundupGamePagesLayout, {
      global: {
        stubs: ['router-view']
      }
    })

    // Test with missing realty_game_listing
    const partialData = {
      sale_listing: mockApiResponse.sale_listing
    }

    wrapper.vm.rawPropertyData = partialData

    await wrapper.vm.$nextTick()

    const currentPropertyListing = wrapper.vm.currentPropertyListing

    expect(currentPropertyListing.realty_game_listing).toEqual({})
    expect(currentPropertyListing.sale_listing).toBeDefined()
    expect(currentPropertyListing.sale_listing.street_address).toBe('Main Street, Cossington, Leicestershire')
  })

  it('should have correct reactive properties for child components', async () => {
    wrapper = mount(RoundupGamePagesLayout, {
      global: {
        stubs: ['router-view']
      }
    })

    wrapper.vm.rawPropertyData = mockApiResponse

    await wrapper.vm.$nextTick()

    // Check that the component has the expected reactive properties
    expect(wrapper.vm.currentPropertyListing).toBeDefined()
    expect(wrapper.vm.isPropertyLoading).toBe(false)
    expect(wrapper.vm.propertyError).toBeNull()

    // Verify the structure is correct for passing to child components
    const listing = wrapper.vm.currentPropertyListing
    expect(listing.realty_game_listing).toBeDefined()
    expect(listing.sale_listing).toBeDefined()
  })
})
