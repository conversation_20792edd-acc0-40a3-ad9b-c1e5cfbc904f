import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ref } from 'vue'
import RoundupGamePropertyPage from '../pages/roundup/RoundupGamePropertyPage.vue'

// Mock the router and route
const mockRoute = {
  name: 'rRoundupGameProperty',
  params: {
    gameSlug: 'test-game',
    listingInGameUuid: 'test-uuid'
  },
  query: {}
}

const mockRouter = {
  push: vi.fn()
}

// Mock composables and dependencies
vi.mock('vue-router', () => ({
  useRoute: () => mockRoute,
  useRouter: () => mockRouter
}))

vi.mock('quasar', () => ({
  useQuasar: () => ({
    notify: vi.fn(),
    dialog: vi.fn()
  })
}))

vi.mock('src/concerns/realty-game/composables/useRealtyGame', () => ({
  useRealtyGame: () => ({
    isLoading: ref(false),
    error: ref(null),
    gameListings: ref([]),
    gameDesc: ref('Test game description'),
    gameBgImageUrl: ref(''),
    getPropertyByIndex: vi.fn(),
    getPropertyByUuid: vi.fn(() => null),
    getPropertyImages: vi.fn(() => []),
    validateGuess: vi.fn(),
    submitGameGuess: vi.fn(),
    getScoreColor: vi.fn(),
    fetchPriceGuessData: vi.fn(),
    fetchPropertyByUuid: vi.fn(),
    setRealtyGameData: vi.fn()
  })
}))

vi.mock('src/concerns/realty-game/composables/useRealtyGameStorage', () => ({
  useRealtyGameStorage: () => ({
    getOrCreateSessionId: vi.fn(() => 'test-session'),
    getSessionData: vi.fn(),
    saveGuess: vi.fn(),
    getGuess: vi.fn(),
    hasGuessed: vi.fn(() => false),
    getCurrencySelection: vi.fn()
  })
}))

vi.mock('src/concerns/realty-game/composables/useCurrencyConverter', () => ({
  useCurrencyConverter: () => ({
    selectedCurrency: ref('GBP'),
    convertPrice: vi.fn((amount) => amount),
    formatPriceWithBothCurrencies: vi.fn()
  })
}))

vi.mock('src/concerns/realty-game/composables/usePlayerName', () => ({
  usePlayerName: () => ({
    playerName: ref('Test Player'),
    generateRandomName: vi.fn()
  })
}))

vi.mock('src/stores/realtyGame', () => ({
  useRealtyGameStore: () => ({
    currentProperty: null,
    isDataLoaded: false
  })
}))

vi.mock('src/stores/realtyGameMeta', () => ({
  useRealtyGameMetaStore: () => ({
    setMetaTags: vi.fn()
  })
}))

vi.mock('src/compose/useJsonLd.js', () => ({
  default: () => ({
    setJsonLd: vi.fn()
  })
}))

// Mock components
vi.mock('src/concerns/price-guess/components/PriceGuessLeafletMap.vue', () => ({
  name: 'PriceGuessLeafletMap',
  template: '<div>Mock Leaflet Map</div>'
}))

vi.mock('src/concerns/price-guess/components/BoundaryLeafletMap.vue', () => ({
  name: 'BoundaryLeafletMap',
  template: '<div>Mock Boundary Map</div>'
}))

vi.mock('src/components/seo/PropertyJsonLd.vue', () => ({
  name: 'PropertyJsonLd',
  template: '<div>Mock Property JSON-LD</div>'
}))

describe('RoundupGamePropertyPage', () => {
  let wrapper

  const mockCurrentPropertyListing = {
    realty_game_listing: {
      uuid: 'game-listing-uuid',
      gl_title_atr: '4 bedroom character property for sale',
      gl_vicinity_atr: 'Cossington, England',
      gl_image_url_atr: 'https://example.com/image.jpg',
      visible_in_game: true
    },
    sale_listing: {
      uuid: 'sale-listing-uuid',
      title: '4 bedroom character property for sale',
      street_address: 'Main Street, Cossington, Leicestershire',
      city: 'Cossington',
      postal_code: 'LE7 4UW',
      country: 'England',
      price_sale_current_cents: 55000000,
      currency: 'GBP',
      count_bedrooms: 4,
      count_bathrooms: 2,
      count_garages: 1,
      description: 'A stunning period cottage...',
      latitude: 52.714286,
      longitude: -1.103609,
      sale_listing_pics: [
        {
          flag_is_hidden: false,
          uuid: 'image-1',
          image_details: {
            url: 'https://example.com/image1.jpg'
          }
        }
      ]
    }
  }

  const defaultProps = {
    listingInGameUuid: 'test-uuid',
    routeSssnId: 'test-session',
    currPlayerSssnId: 'test-player-session',
    gameTitle: 'Test Game',
    gameDefaultCurrency: 'GBP',
    totalProperties: 5,
    currentPropertyListing: mockCurrentPropertyListing,
    isPropertyLoading: false,
    propertyError: null
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should create currentProperty with merged structure for backward compatibility', async () => {
    wrapper = mount(RoundupGamePropertyPage, {
      props: defaultProps,
      global: {
        stubs: {
          'PriceGuessLeafletMap': true,
          'BoundaryLeafletMap': true,
          'PropertyJsonLd': true
        }
      }
    })

    await wrapper.vm.$nextTick()

    const currentProperty = wrapper.vm.currentProperty

    // Test that both nested and flat access work
    expect(currentProperty).toBeDefined()

    // Flat access (backward compatibility)
    expect(currentProperty.gl_title_atr).toBe('4 bedroom character property for sale')
    expect(currentProperty.title).toBe('4 bedroom character property for sale')
    expect(currentProperty.street_address).toBe('Main Street, Cossington, Leicestershire')
    expect(currentProperty.count_bedrooms).toBe(4)
    expect(currentProperty.price_sale_current_cents).toBe(55000000)

    // Nested access (new structure)
    expect(currentProperty.realty_game_listing).toBeDefined()
    expect(currentProperty.sale_listing).toBeDefined()
    expect(currentProperty.realty_game_listing.gl_title_atr).toBe('4 bedroom character property for sale')
    expect(currentProperty.sale_listing.street_address).toBe('Main Street, Cossington, Leicestershire')
  })

  it('should display property information correctly in template', async () => {
    wrapper = mount(RoundupGamePropertyPage, {
      props: defaultProps,
      global: {
        stubs: {
          'PriceGuessLeafletMap': true,
          'BoundaryLeafletMap': true,
          'PropertyJsonLd': true
        }
      }
    })

    await wrapper.vm.$nextTick()

    // Check that property title is displayed
    expect(wrapper.text()).toContain('4 bedroom character property for sale')
    
    // Check that address is displayed
    expect(wrapper.text()).toContain('Main Street, Cossington, Leicestershire')
    
    // Check that bedroom count is displayed
    expect(wrapper.text()).toContain('4 Bedrooms')
    
    // Check that bathroom count is displayed
    expect(wrapper.text()).toContain('2 Bathrooms')
  })

  it('should handle missing currentPropertyListing gracefully', async () => {
    const propsWithoutListing = {
      ...defaultProps,
      currentPropertyListing: null
    }

    wrapper = mount(RoundupGamePropertyPage, {
      props: propsWithoutListing,
      global: {
        stubs: {
          'PriceGuessLeafletMap': true,
          'BoundaryLeafletMap': true,
          'PropertyJsonLd': true
        }
      }
    })

    await wrapper.vm.$nextTick()

    // Should fallback to other data sources or empty object
    const currentProperty = wrapper.vm.currentProperty
    expect(currentProperty).toBeDefined()
  })

  it('should handle partial data structures', async () => {
    const partialListing = {
      realty_game_listing: {
        gl_title_atr: 'Test Property'
      },
      sale_listing: {
        title: 'Test Property',
        city: 'Test City'
      }
    }

    const propsWithPartialData = {
      ...defaultProps,
      currentPropertyListing: partialListing
    }

    wrapper = mount(RoundupGamePropertyPage, {
      props: propsWithPartialData,
      global: {
        stubs: {
          'PriceGuessLeafletMap': true,
          'BoundaryLeafletMap': true,
          'PropertyJsonLd': true
        }
      }
    })

    await wrapper.vm.$nextTick()

    const currentProperty = wrapper.vm.currentProperty

    // Should handle missing properties gracefully
    expect(currentProperty.gl_title_atr).toBe('Test Property')
    expect(currentProperty.title).toBe('Test Property')
    expect(currentProperty.city).toBe('Test City')
    expect(currentProperty.street_address).toBeUndefined()
  })

  it('should filter property images correctly', async () => {
    const listingWithImages = {
      ...mockCurrentPropertyListing,
      sale_listing: {
        ...mockCurrentPropertyListing.sale_listing,
        sale_listing_pics: [
          {
            flag_is_hidden: false,
            uuid: 'image-1',
            image_details: { url: 'https://example.com/image1.jpg' }
          },
          {
            flag_is_hidden: true,
            uuid: 'image-2',
            image_details: { url: 'https://example.com/image2.jpg' }
          },
          {
            flag_is_hidden: false,
            uuid: 'image-3',
            image_details: { url: 'https://example.com/image3.jpg' }
          }
        ]
      }
    }

    const propsWithImages = {
      ...defaultProps,
      currentPropertyListing: listingWithImages
    }

    // Mock getPropertyImages to return the images
    const { useRealtyGame } = await import('src/concerns/realty-game/composables/useRealtyGame')
    const mockUseRealtyGame = useRealtyGame()
    mockUseRealtyGame.getPropertyImages.mockReturnValue(listingWithImages.sale_listing.sale_listing_pics)

    wrapper = mount(RoundupGamePropertyPage, {
      props: propsWithImages,
      global: {
        stubs: {
          'PriceGuessLeafletMap': true,
          'BoundaryLeafletMap': true,
          'PropertyJsonLd': true
        }
      }
    })

    await wrapper.vm.$nextTick()

    const propertyImages = wrapper.vm.propertyImages

    // Should only include non-hidden images
    expect(propertyImages).toHaveLength(2)
    expect(propertyImages.every(img => !img.flag_is_hidden)).toBe(true)
  })
})
