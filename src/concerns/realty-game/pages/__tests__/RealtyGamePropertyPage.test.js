import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import RealtyGamePropertyPage from '../RealtyGamePropertyPage.vue'
import {
  createMockProperty,
  createMockRealtyGame,
  createMockRealtyGameStorage,
  createMockCurrencyConverter,
  createMockStore,
  flushPromises
} from 'src/test/utils.js'

// Mock the composables
vi.mock('src/concerns/realty-game/composables/useRealtyGame', () => ({
  useRealtyGame: vi.fn()
}))

vi.mock('src/concerns/realty-game/composables/useRealtyGameStorage', () => ({
  useRealtyGameStorage: vi.fn()
}))

vi.mock('src/concerns/realty-game/composables/useCurrencyConverter', () => ({
  useCurrencyConverter: vi.fn()
}))

vi.mock('src/stores/realtyGame', () => ({
  useRealtyGameStore: vi.fn()
}))

vi.mock('src/stores/realtyGameMeta', () => ({
  useRealtyGameMetaStore: vi.fn()
}))

describe('RealtyGamePropertyPage', () => {
  let mockRealtyGame
  let mockStorage
  let mockCurrency
  let mockStore
  let wrapper

  beforeEach(async () => {
    // Reset all mocks
    vi.clearAllMocks()
    
    // Create fresh mock instances
    mockRealtyGame = createMockRealtyGame()
    mockStorage = createMockRealtyGameStorage()
    mockCurrency = createMockCurrencyConverter()
    mockStore = createMockStore()

    // Setup mock implementations
    const { useRealtyGame } = await import('src/concerns/realty-game/composables/useRealtyGame')
    const { useRealtyGameStorage } = await import('src/concerns/realty-game/composables/useRealtyGameStorage')
    const { useCurrencyConverter } = await import('src/concerns/realty-game/composables/useCurrencyConverter')
    const { useRealtyGameStore } = await import('src/stores/realtyGame')

    useRealtyGame.mockReturnValue(mockRealtyGame)
    useRealtyGameStorage.mockReturnValue(mockStorage)
    useCurrencyConverter.mockReturnValue(mockCurrency)
    useRealtyGameStore.mockReturnValue(mockStore)
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  const createWrapper = (props = {}) => {
    return mount(RealtyGamePropertyPage, {
      props: {
        listingInGameUuid: 'test-property-uuid',
        routeSssnId: 'test-session-id',
        ...props
      }
    })
  }

  describe('Component Mounting and Initial State', () => {
    it('should mount successfully', () => {
      wrapper = createWrapper()
      expect(wrapper.exists()).toBe(true)
    })

    it('should display loading state when data is loading', () => {
      mockRealtyGame.isLoading = true
      wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="loading-container"]').exists()).toBe(true)
      expect(wrapper.text()).toContain('Loading Property...')
    })

    it('should display error state when there is an error', () => {
      mockRealtyGame.error = 'Failed to load property'
      wrapper = createWrapper()
      
      expect(wrapper.find('[data-testid="error-container"]').exists()).toBe(true)
      expect(wrapper.text()).toContain('Error Loading Property')
      expect(wrapper.text()).toContain('Failed to load property')
    })

    it('should display property content when data is loaded', async () => {
      wrapper = createWrapper()
      await nextTick()
      
      expect(wrapper.find('.property-content').exists()).toBe(true)
      expect(wrapper.text()).toContain('Beautiful Test Property')
    })
  })

  describe('Property Data Display', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should display property title and address', () => {
      expect(wrapper.text()).toContain('Beautiful Test Property')
      expect(wrapper.text()).toContain('123 Test Street')
    })

    it('should display property features', () => {
      expect(wrapper.text()).toContain('3 Bedrooms')
      expect(wrapper.text()).toContain('2 Bathrooms') 
      expect(wrapper.text()).toContain('1 Garages')
    })

    it('should display property description', () => {
      expect(wrapper.text()).toContain('A lovely property for testing')
    })

    it('should display property images in carousel', () => {
      const carousel = wrapper.find('div[class*="q-carousel"]')
      expect(carousel.exists()).toBe(true)
      
      const slides = wrapper.findAll('div[class*="q-carousel-slide"]')
      expect(slides).toHaveLength(2) // Based on mock data
    })

    it('should display property progress', () => {
      expect(wrapper.text()).toContain('Property 1 of 1')
      expect(wrapper.text()).toContain('100% Complete')
    })
  })

  describe('Price Guessing Functionality', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should display price input form when property not guessed', () => {
      const priceInput = wrapper.find('input[data-testid="input"]')
      expect(priceInput.exists()).toBe(true)
      
      const submitButton = wrapper.find('button[label="Submit Guess"]')
      expect(submitButton.exists()).toBe(true)
    })

    it('should validate guess input', async () => {
      const priceInput = wrapper.find('input[data-testid="input"]')
      
      // Test validation with numeric input
      mockRealtyGame.validateGuess.mockReturnValue({
        isValid: false,
        errors: ['Guess must be a positive number']
      })
      
      await priceInput.setValue('100000') // Use valid numeric input
      await wrapper.find('button[label="Submit Guess"]').trigger('click')
      
      expect(mockRealtyGame.validateGuess).toHaveBeenCalled()
    })

    it('should submit guess successfully', async () => {
      const priceInput = wrapper.find('input[data-testid="input"]')
      const submitButton = wrapper.find('button[label="Submit Guess"]')
      
      // Make sure validation passes
      mockRealtyGame.validateGuess.mockReturnValue({
        isValid: true,
        errors: []
      })
      
      await priceInput.setValue('450000')
      await submitButton.trigger('click')
      await flushPromises()
      
      expect(mockRealtyGame.submitGameGuess).toHaveBeenCalledWith(
        450000,
        expect.objectContaining({
          name: 'Test Player',
          sessionId: 'test-session-id'
        }),
        expect.any(Object),
        0
      )
      
      expect(mockStorage.saveGuess).toHaveBeenCalled()
    })

    it('should display already guessed state', () => {
      mockStorage.hasGuessed.mockReturnValue(true)
      mockStorage.getGuess.mockReturnValue({
        guess: 450000,
        score: 85,
        difference: -10.0
      })
      
      wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('Your Previous Guess')
      expect(wrapper.text()).toContain('Great job! You\'ve already made a guess')
      expect(wrapper.find('button[label="Submit Guess"]').exists()).toBe(false)
    })
  })

  describe('Navigation and Progress', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should emit progress update on mount', () => {
      expect(wrapper.emitted('update-progress')).toBeTruthy()
      expect(wrapper.emitted('update-progress')[0]).toEqual([{
        currentIndex: 0,
        total: 1
      }])
    })

    it('should show next property button when not last property', () => {
      mockRealtyGame.totalProperties = 5
      wrapper = createWrapper()
      
      const nextButton = wrapper.find('button[label="Next Property"]')
      expect(nextButton.exists()).toBe(true)
    })

    it('should show view results button when last property', () => {
      mockStorage.hasGuessed.mockReturnValue(true)
      wrapper = createWrapper()
      
      const resultsButton = wrapper.find('button[label="View Results"]')
      expect(resultsButton.exists()).toBe(true)
    })
  })

  describe('Data Fetching on Direct URL Load', () => {
    it('should fetch game data when store is empty', async () => {
      mockStore.isDataLoaded = false
      mockStore.gameListings = []
      
      wrapper = createWrapper()
      await flushPromises()
      
      expect(mockRealtyGame.fetchPriceGuessData).toHaveBeenCalled()
    })

    it('should fetch individual property data when not in store', async () => {
      mockStore.currentProperty = null
      
      wrapper = createWrapper()
      await flushPromises()
      
      expect(mockRealtyGame.fetchPropertyByUuid).toHaveBeenCalledWith('test-property-uuid')
    })

    it('should use store data when available', async () => {
      mockStore.currentProperty = createMockProperty({ uuid: 'store-property' })
      
      wrapper = createWrapper()
      await nextTick()
      
      expect(mockRealtyGame.fetchPropertyByUuid).not.toHaveBeenCalled()
    })
  })

  describe('Property UUID Changes', () => {
    it('should fetch new property data when UUID changes', async () => {
      wrapper = createWrapper({ listingInGameUuid: 'property-1' })
      await flushPromises()
      
      // Change property UUID
      await wrapper.setProps({ listingInGameUuid: 'property-2' })
      await flushPromises()
      
      expect(mockRealtyGame.fetchPropertyByUuid).toHaveBeenCalledWith('property-2')
    })

    it('should not fetch if property already in store', async () => {
      mockStore.currentProperty = createMockProperty({ uuid: 'property-1' })
      
      wrapper = createWrapper({ listingInGameUuid: 'property-1' })
      await flushPromises()
      
      await wrapper.setProps({ listingInGameUuid: 'property-1' })
      await flushPromises()
      
      expect(mockRealtyGame.fetchPropertyByUuid).not.toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should display fallback error when no property data', () => {
      mockStore.currentProperty = null
      mockRealtyGame.getPropertyByUuid.mockReturnValue(null)
      
      wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('Property Not Found')
      expect(wrapper.text()).toContain('Unable to load property data')
    })

    it('should handle fetch errors gracefully', async () => {
      mockRealtyGame.fetchPropertyByUuid.mockRejectedValue(new Error('Network error'))
      mockStore.currentProperty = null
      
      wrapper = createWrapper()
      await flushPromises()
      
      // Should not crash and should show fallback
      expect(wrapper.text()).toContain('Property Not Found')
    })
  })

  describe('Currency Handling', () => {
    it('should initialize currency from session', () => {
      mockStorage.getCurrencySelection.mockReturnValue('USD')
      
      wrapper = createWrapper()
      
      expect(mockCurrency.setCurrency).toHaveBeenCalledWith('USD')
    })

    it('should display prices in selected currency', () => {
      mockCurrency.formatPriceWithBothCurrencies.mockReturnValue('$500,000')
      
      wrapper = createWrapper()
      
      expect(mockCurrency.formatPriceWithBothCurrencies).toHaveBeenCalled()
    })
  })

  describe('Feedback Dialog', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should show feedback dialog after successful guess', async () => {
      // Simulate successful guess submission
      await wrapper.find('input[data-testid="input"]').setValue('450000')
      await wrapper.find('button[label="Submit Guess"]').trigger('click')
      await flushPromises()
      
      const dialog = wrapper.find('div[class*="q-dialog"]')
      expect(dialog.exists()).toBe(true)
      expect(wrapper.text()).toContain('Guess Result')
    })

    it('should display guess results in dialog', async () => {
      // Set up state as if guess was just submitted
      wrapper.vm.currentResult = {
        score: 85,
        difference: -10.0,
        feedback: 'Great guess!',
        savedEstimate: {
          guessed_price: {
            guessed_price_in_ui_currency_cents: 45000000
          }
        }
      }
      wrapper.vm.showGameResultPopup = true
      
      await nextTick()
      
      expect(wrapper.text()).toContain('Score: 85/100')
      expect(wrapper.text()).toContain('Great guess!')
    })
  })

  describe('Accessibility and UX', () => {
    beforeEach(() => {
      wrapper = createWrapper()
    })

    it('should have proper loading states', () => {
      wrapper.vm.isFetchingProperty = true
      wrapper.vm.$forceUpdate()
      
      expect(wrapper.find('.loading-container').exists()).toBe(true)
    })

    it('should disable submit button when no guess entered', () => {
      const submitButton = wrapper.find('button[label="Submit Guess"]')
      expect(submitButton.attributes('disabled')).toBeDefined()
    })

    it('should enable submit button when guess is entered', async () => {
      await wrapper.find('input[data-testid="input"]').setValue('450000')
      
      const submitButton = wrapper.find('button[label="Submit Guess"]')
      expect(submitButton.attributes('disabled')).toBeUndefined()
    })
  })

  describe('JSON-LD and SEO', () => {
    it('should render PropertyJsonLd component when property exists', () => {
      wrapper = createWrapper()
      
      const jsonLdComponent = wrapper.findComponent('[data-testid="property-json-ld"]')
      expect(jsonLdComponent.exists()).toBe(true)
    })

    it('should not render PropertyJsonLd when no property', () => {
      mockStore.currentProperty = null
      mockRealtyGame.getPropertyByUuid.mockReturnValue(null)
      
      wrapper = createWrapper()
      
      const jsonLdComponent = wrapper.findComponent('[data-testid="property-json-ld"]')
      expect(jsonLdComponent.exists()).toBe(false)
    })
  })
})
