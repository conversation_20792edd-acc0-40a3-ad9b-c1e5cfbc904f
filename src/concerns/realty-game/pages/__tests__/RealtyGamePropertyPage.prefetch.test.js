import { describe, it, expect, vi, beforeEach } from 'vitest'
import axios from 'axios'
import { createMockProperty, createMockGameData } from 'src/test/utils.js'

// Import the component to access preFetch
import RealtyGamePropertyPage from '../RealtyGamePropertyPage.vue'

// Mock Pinia stores
const mockRealtyGameStore = {
  setRealtyGameData: vi.fn()
}

const mockRealtyGameMetaStore = {
  setMeta: vi.fn()
}

vi.mock('src/stores/realtyGame', () => ({
  useRealtyGameStore: () => mockRealtyGameStore
}))

vi.mock('src/stores/realtyGameMeta', () => ({
  useRealtyGameMetaStore: () => mockRealtyGameMetaStore
}))

describe('RealtyGamePropertyPage - preFetch', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    axios.get.mockClear()
  })

  const createMockRoute = (gameSlug = 'test-game', listingInGameUuid = 'test-property') => ({
    path: `/game/${gameSlug}/property/${listingInGameUuid}`,
    params: { gameSlug, listingInGameUuid },
    fullPath: `/game/${gameSlug}/property/${listingInGameUuid}`
  })

  const createMockGameResponse = () => ({
    data: {
      price_guess_inputs: {
        game_listings: [
          {
            listing_details: {
              ...createMockProperty(),
              visible: true
            }
          }
        ]
      },
      realty_game_details: {
        game_title: 'Test Property Game',
        game_description: 'Test your property knowledge',
        game_bg_image_url: 'https://example.com/bg.jpg'
      }
    }
  })

  const createMockPropertyResponse = () => ({
    data: {
      sale_listing: createMockProperty({
        photos: [
          {
            image_details: { url: 'https://example.com/photo1.jpg' },
            flag_is_hidden: false
          }
        ]
      })
    }
  })

  describe('Successful preFetch', () => {
    it('should fetch game and property data successfully', async () => {
      const mockRoute = createMockRoute()
      const gameResponse = createMockGameResponse()
      const propertyResponse = createMockPropertyResponse()

      axios.get
        .mockResolvedValueOnce(gameResponse)
        .mockResolvedValueOnce(propertyResponse)

      const result = await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext: null
      })

      expect(axios.get).toHaveBeenCalledTimes(2)
      expect(axios.get).toHaveBeenCalledWith(
        'https://api.test.com/api_public/v4/realty_game_summary/test-game'
      )
      expect(axios.get).toHaveBeenCalledWith(
        'https://api.test.com/api_public/v4/game_sale_listings/show_rgl/test-property'
      )

      expect(result).toEqual({
        gameData: gameResponse.data,
        propertyData: propertyResponse.data
      })
    })

    it('should update Pinia stores with fetched data', async () => {
      const mockRoute = createMockRoute()
      const gameResponse = createMockGameResponse()
      const propertyResponse = createMockPropertyResponse()

      axios.get
        .mockResolvedValueOnce(gameResponse)
        .mockResolvedValueOnce(propertyResponse)

      await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext: null
      })

      expect(mockRealtyGameStore.setRealtyGameData).toHaveBeenCalledWith({
        gameListings: expect.arrayContaining([
          expect.objectContaining({
            uuid: 'test-property-uuid',
            title: 'Beautiful Test Property'
          })
        ]),
        gameTitle: 'Test Property Game',
        gameDesc: 'Test your property knowledge',
        gameBgImageUrl: 'https://example.com/bg.jpg',
        currentProperty: expect.objectContaining({
          uuid: 'test-property-uuid',
          title: 'Beautiful Test Property'
        })
      })
    })

    it('should set meta tags', async () => {
      const mockRoute = createMockRoute()
      const gameResponse = createMockGameResponse()
      const propertyResponse = createMockPropertyResponse()

      axios.get
        .mockResolvedValueOnce(gameResponse)
        .mockResolvedValueOnce(propertyResponse)

      await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext: null
      })

      expect(mockRealtyGameMetaStore.setMeta).toHaveBeenCalledWith({
        title: expect.stringContaining('Beautiful Test Property'),
        description: expect.stringContaining('Test your property knowledge'),
        image: 'https://example.com/photo1.jpg',
        url: expect.stringContaining('/game/test-game/property/test-property'),
        keywords: expect.stringContaining('property price game'),
        type: 'website',
        twitterCard: 'summary_large_image'
      })
    })

    it('should filter visible game listings only', async () => {
      const mockRoute = createMockRoute()
      const gameResponse = {
        data: {
          price_guess_inputs: {
            game_listings: [
              {
                listing_details: {
                  ...createMockProperty({ uuid: 'visible-property' }),
                  visible: true
                }
              },
              {
                listing_details: {
                  ...createMockProperty({ uuid: 'hidden-property' }),
                  visible: false
                }
              }
            ]
          },
          realty_game_details: {
            game_title: 'Test Game',
            game_description: 'Test Description',
            game_bg_image_url: 'https://example.com/bg.jpg'
          }
        }
      }
      const propertyResponse = createMockPropertyResponse()

      axios.get
        .mockResolvedValueOnce(gameResponse)
        .mockResolvedValueOnce(propertyResponse)

      await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext: null
      })

      const setDataCall = mockRealtyGameStore.setRealtyGameData.mock.calls[0][0]
      expect(setDataCall.gameListings).toHaveLength(1)
      expect(setDataCall.gameListings[0].uuid).toBe('visible-property')
    })
  })

  describe('preFetch error handling', () => {
    it('should handle missing route parameters', async () => {
      const mockRoute = {
        path: '/invalid',
        params: {},
        fullPath: '/invalid'
      }

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      const result = await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext: null
      })

      expect(result).toBeUndefined()
      expect(consoleSpy).toHaveBeenCalledWith(
        'Missing gameSlug or listingInGameUuid in preFetch. Route:',
        expect.any(String),
        'Params:',
        expect.any(Object)
      )

      consoleSpy.mockRestore()
    })

    it('should handle API errors gracefully', async () => {
      const mockRoute = createMockRoute()
      
      axios.get.mockRejectedValue(new Error('API Error'))

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const result = await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext: null
      })

      expect(result).toBeNull()
      expect(consoleSpy).toHaveBeenCalledWith(
        'Prefetch error:',
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })

    it('should handle partial API failures', async () => {
      const mockRoute = createMockRoute()
      const gameResponse = createMockGameResponse()

      // First call succeeds, second fails
      axios.get
        .mockResolvedValueOnce(gameResponse)
        .mockRejectedValueOnce(new Error('Property API Error'))

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      const result = await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext: null
      })

      expect(result).toBeNull()
      expect(consoleSpy).toHaveBeenCalled()

      consoleSpy.mockRestore()
    })
  })

  describe('SSR Context', () => {
    it('should populate SSR context when provided', async () => {
      const mockRoute = createMockRoute()
      const gameResponse = createMockGameResponse()
      const propertyResponse = createMockPropertyResponse()
      const ssrContext = {}

      axios.get
        .mockResolvedValueOnce(gameResponse)
        .mockResolvedValueOnce(propertyResponse)

      await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext
      })

      expect(ssrContext.gameData).toEqual({
        gameTitle: 'Test Property Game',
        gameDesc: 'Test your property knowledge',
        gameBgImageUrl: 'https://example.com/bg.jpg',
        currentProperty: expect.objectContaining({
          uuid: 'test-property-uuid'
        }),
        listingInGameUuid: 'test-property',
        totalProperties: 1
      })
    })

    it('should work without SSR context', async () => {
      const mockRoute = createMockRoute()
      const gameResponse = createMockGameResponse()
      const propertyResponse = createMockPropertyResponse()

      axios.get
        .mockResolvedValueOnce(gameResponse)
        .mockResolvedValueOnce(propertyResponse)

      // Should not throw when ssrContext is null
      expect(async () => {
        await RealtyGamePropertyPage.preFetch({
          currentRoute: mockRoute,
          ssrContext: null
        })
      }).not.toThrow()
    })
  })

  describe('Console Logging', () => {
    it('should log preFetch execution details', async () => {
      const mockRoute = createMockRoute('my-game', 'my-property')
      const gameResponse = createMockGameResponse()
      const propertyResponse = createMockPropertyResponse()

      axios.get
        .mockResolvedValueOnce(gameResponse)
        .mockResolvedValueOnce(propertyResponse)

      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {})

      await RealtyGamePropertyPage.preFetch({
        currentRoute: mockRoute,
        ssrContext: null
      })

      expect(consoleSpy).toHaveBeenCalledWith(
        'preFetch function called! Route:',
        '/game/my-game/property/my-property',
        'Params:',
        { gameSlug: 'my-game', listingInGameUuid: 'my-property' }
      )

      expect(consoleSpy).toHaveBeenCalledWith(
        'preFetch called with gameSlug:',
        'my-game',
        'listingInGameUuid:',
        'my-property'
      )

      consoleSpy.mockRestore()
    })
  })
})
