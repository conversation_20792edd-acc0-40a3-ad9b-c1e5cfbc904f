import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import RealtyGamePropertyPage from '../RealtyGamePropertyPage.vue'
import {
  createMockProperty,
  createMockRealtyGame,
  createMockRealtyGameStorage,
  createMockCurrencyConverter,
  createMockStore,
  flushPromises
} from 'src/test/utils.js'

// Mock the composables
vi.mock('src/concerns/realty-game/composables/useRealtyGame')
vi.mock('src/concerns/realty-game/composables/useRealtyGameStorage')
vi.mock('src/concerns/realty-game/composables/useCurrencyConverter')
vi.mock('src/stores/realtyGame')
vi.mock('src/stores/realtyGameMeta')

describe('RealtyGamePropertyPage - Data Fetching Integration', () => {
  let mockRealtyGame
  let mockStorage
  let mockCurrency
  let mockStore
  let wrapper

  beforeEach(async () => {
    vi.clearAllMocks()
    
    mockRealtyGame = createMockRealtyGame()
    mockStorage = createMockRealtyGameStorage()
    mockCurrency = createMockCurrencyConverter()
    mockStore = createMockStore()

    const { useRealtyGame } = await import('src/concerns/realty-game/composables/useRealtyGame')
    const { useRealtyGameStorage } = await import('src/concerns/realty-game/composables/useRealtyGameStorage')
    const { useCurrencyConverter } = await import('src/concerns/realty-game/composables/useCurrencyConverter')
    const { useRealtyGameStore } = await import('src/stores/realtyGame')

    useRealtyGame.mockReturnValue(mockRealtyGame)
    useRealtyGameStorage.mockReturnValue(mockStorage)
    useCurrencyConverter.mockReturnValue(mockCurrency)
    useRealtyGameStore.mockReturnValue(mockStore)
  })

  const createWrapper = (props = {}) => {
    return mount(RealtyGamePropertyPage, {
      props: {
        listingInGameUuid: 'test-property-uuid',
        routeSssnId: 'test-session-id',
        ...props
      }
    })
  }

  describe('Direct URL Loading Scenarios', () => {
    it('should handle empty store and fetch all required data', async () => {
      // Simulate direct URL load with empty store
      mockStore.isDataLoaded = false
      mockStore.gameListings = []
      mockStore.currentProperty = null
      
      wrapper = createWrapper()
      await flushPromises()
      
      // Should fetch game data
      expect(mockRealtyGame.fetchPriceGuessData).toHaveBeenCalled()
      
      // Should fetch individual property data
      expect(mockRealtyGame.fetchPropertyByUuid).toHaveBeenCalledWith('test-property-uuid')
    })

    it('should use preFetched data when available', async () => {
      // Simulate successful preFetch
      mockStore.isDataLoaded = true
      mockStore.currentProperty = createMockProperty()
      mockStore.gameListings = [createMockProperty()]
      
      wrapper = createWrapper()
      await flushPromises()
      
      // Should not fetch data as it's already in store
      expect(mockRealtyGame.fetchPriceGuessData).not.toHaveBeenCalled()
      expect(mockRealtyGame.fetchPropertyByUuid).not.toHaveBeenCalled()
    })

    it('should handle partial data scenarios', async () => {
      // Game data available but no current property
      mockStore.isDataLoaded = true
      mockStore.gameListings = [createMockProperty()]
      mockStore.currentProperty = null
      
      wrapper = createWrapper()
      await flushPromises()
      
      // Should not fetch game data
      expect(mockRealtyGame.fetchPriceGuessData).not.toHaveBeenCalled()
      
      // Should fetch individual property data
      expect(mockRealtyGame.fetchPropertyByUuid).toHaveBeenCalledWith('test-property-uuid')
    })
  })

  describe('Property UUID Change Handling', () => {
    it('should fetch new property when UUID changes', async () => {
      wrapper = createWrapper({ listingInGameUuid: 'property-1' })
      await flushPromises()
      
      // Clear previous calls
      vi.clearAllMocks()
      
      // Change to new property
      await wrapper.setProps({ listingInGameUuid: 'property-2' })
      await flushPromises()
      
      expect(mockRealtyGame.fetchPropertyByUuid).toHaveBeenCalledWith('property-2')
    })

    it('should not fetch if same UUID', async () => {
      wrapper = createWrapper({ listingInGameUuid: 'property-1' })
      await flushPromises()
      
      vi.clearAllMocks()
      
      // Set same UUID
      await wrapper.setProps({ listingInGameUuid: 'property-1' })
      await flushPromises()
      
      expect(mockRealtyGame.fetchPropertyByUuid).not.toHaveBeenCalled()
    })

    it('should skip fetch if property already in store', async () => {
      mockStore.currentProperty = createMockProperty({ uuid: 'property-2' })
      
      wrapper = createWrapper({ listingInGameUuid: 'property-1' })
      await flushPromises()
      
      vi.clearAllMocks()
      
      // Change to property that's already in store
      await wrapper.setProps({ listingInGameUuid: 'property-2' })
      await flushPromises()
      
      expect(mockRealtyGame.fetchPropertyByUuid).not.toHaveBeenCalled()
    })
  })

  describe('Loading States During Data Fetching', () => {
    it('should show loading state while fetching property data', async () => {
      let resolvePromise
      const fetchPromise = new Promise(resolve => {
        resolvePromise = resolve
      })
      
      mockRealtyGame.fetchPropertyByUuid.mockReturnValue(fetchPromise)
      mockStore.currentProperty = null
      
      wrapper = createWrapper()
      await nextTick()
      
      // Should show loading state
      expect(wrapper.find('.loading-container').exists()).toBe(true)
      expect(wrapper.text()).toContain('Loading Property...')
      
      // Resolve the fetch
      resolvePromise({ sale_listing: createMockProperty() })
      await flushPromises()
      
      // Should hide loading state
      expect(wrapper.find('.loading-container').exists()).toBe(false)
    })

    it('should handle fetch errors gracefully', async () => {
      mockRealtyGame.fetchPropertyByUuid.mockRejectedValue(new Error('Network error'))
      mockStore.currentProperty = null
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
      
      wrapper = createWrapper()
      await flushPromises()
      
      // Should log error but not crash
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to load individual property data:',
        expect.any(Error)
      )
      
      // Should show fallback state
      expect(wrapper.text()).toContain('Property Not Found')
      
      consoleSpy.mockRestore()
    })
  })

  describe('Data Resolution Priority', () => {
    it('should prioritize store data over fetched data', async () => {
      const storeProperty = createMockProperty({ 
        uuid: 'store-property', 
        title: 'Store Property' 
      })
      const fetchedProperty = createMockProperty({ 
        uuid: 'fetched-property', 
        title: 'Fetched Property' 
      })
      
      mockStore.currentProperty = storeProperty
      mockRealtyGame.fetchPropertyByUuid.mockResolvedValue({
        sale_listing: fetchedProperty
      })
      
      wrapper = createWrapper()
      await flushPromises()
      
      // Should display store property, not fetched property
      expect(wrapper.text()).toContain('Store Property')
      expect(wrapper.text()).not.toContain('Fetched Property')
    })

    it('should use fetched data when store is empty', async () => {
      const fetchedProperty = createMockProperty({ 
        title: 'Fetched Property' 
      })
      
      mockStore.currentProperty = null
      mockRealtyGame.fetchPropertyByUuid.mockResolvedValue({
        sale_listing: fetchedProperty
      })
      
      wrapper = createWrapper()
      await flushPromises()
      
      expect(wrapper.text()).toContain('Fetched Property')
    })

    it('should fall back to basic property data when detailed data unavailable', async () => {
      const basicProperty = createMockProperty({ 
        title: 'Basic Property',
        description: null, // No detailed data
        photos: []
      })
      
      mockStore.currentProperty = null
      mockRealtyGame.fetchPropertyByUuid.mockResolvedValue(null)
      mockRealtyGame.getPropertyByUuid.mockReturnValue(basicProperty)
      
      wrapper = createWrapper()
      await flushPromises()
      
      expect(wrapper.text()).toContain('Basic Property')
    })
  })

  describe('Route Parameter Handling', () => {
    it('should extract gameSlug from route for data fetching', async () => {
      const mockRoute = {
        params: { gameSlug: 'test-game-slug' },
        path: '/game/test-game-slug/property/test-uuid',
        matched: []
      }
      
      const { useRoute } = await import('vue-router')
      useRoute.mockReturnValue(mockRoute)
      
      mockStore.isDataLoaded = false
      
      wrapper = createWrapper()
      await flushPromises()
      
      expect(mockRealtyGame.fetchPriceGuessData).toHaveBeenCalledWith('test-game-slug')
    })

    it('should handle missing gameSlug gracefully', async () => {
      const mockRoute = {
        params: {},
        path: '/property/test-uuid',
        matched: []
      }
      
      const { useRoute } = await import('vue-router')
      useRoute.mockReturnValue(mockRoute)
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
      
      mockStore.isDataLoaded = false
      
      wrapper = createWrapper()
      await flushPromises()
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('No gameSlug available for data fetching')
      )
      
      consoleSpy.mockRestore()
    })
  })
})
