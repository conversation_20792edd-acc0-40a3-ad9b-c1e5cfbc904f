import { ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import { useRealtyGameStorage } from './useRealtyGameStorage'

export function usePlayerName() {
  const $q = useQuasar()
  const { getSessionData, saveSessionData } = useRealtyGameStorage()

  // Reactive state
  const playerName = ref(getSessionData().playerName || '')

  // Computed properties
  const isPlayerNameValid = computed(() => {
    return playerName.value && 
           playerName.value.trim() !== '' && 
           playerName.value !== 'Anonymous Player'
  })

  const displayName = computed(() => {
    return playerName.value || 'Anonymous Player'
  })

  // Generate a random player name
  const generateRandomName = () => {
    const adjectives = [
      'Clever',
      'Smart',
      'Wise',
      'Sharp',
      'Keen',
      'Bright',
      'Bold',
      'Quick',
      'Savvy',
      'Astute'
    ]
    const nouns = [
      'Player',
      'Gamer',
      'Explorer',
      'Detective',
      'Expert',
      'Analyst',
      'Investor',
      'Scout',
      'Hunter',
      'Seeker'
    ]
    const randomAdjective = adjectives[Math.floor(Math.random() * adjectives.length)]
    const randomNoun = nouns[Math.floor(Math.random() * nouns.length)]
    const randomNumber = Math.floor(Math.random() * 999) + 1
    return `${randomAdjective} ${randomNoun} ${randomNumber}`
  }

  // Update player name and save to storage
  const updatePlayerName = (newName) => {
    const finalName = newName?.trim() || 'Anonymous Player'
    playerName.value = finalName
    saveSessionData({ playerName: finalName })
  }

  // Show player name dialog and return a promise that resolves when name is set
  const promptForPlayerName = (options = {}) => {
    return new Promise((resolve, reject) => {
      const randomName = generateRandomName()
      const title = options.title || 'Choose Your Player Name'
      const message = options.message || 
        `<p>We have generated a name for you: <strong>${randomName}</strong></p><p>You can use this or create your own.</p>`
      
      $q.dialog({
        title,
        message,
        html: true,
        prompt: { 
          model: randomName, 
          type: 'text',
          placeholder: 'Enter your player name'
        },
        ok: { 
          label: options.okLabel || "Let's Go!", 
          color: 'primary' 
        },
        cancel: options.allowCancel ? { 
          label: 'Cancel', 
          color: 'grey' 
        } : false,
        persistent: !options.allowCancel,
      })
      .onOk((name) => {
        const finalName = name?.trim() || randomName
        updatePlayerName(finalName)
        resolve(finalName)
      })
      .onCancel(() => {
        if (options.allowCancel) {
          reject(new Error('Player name dialog cancelled'))
        }
      })
    })
  }

  // Ensure player has a valid name, prompt if not
  const ensurePlayerName = async (options = {}) => {
    if (isPlayerNameValid.value) {
      return playerName.value
    }
    
    try {
      return await promptForPlayerName(options)
    } catch (error) {
      // If cancelled or error, use current name or Anonymous Player
      const fallbackName = playerName.value || 'Anonymous Player'
      updatePlayerName(fallbackName)
      return fallbackName
    }
  }

  // Initialize player name from storage
  const initializePlayerName = () => {
    const sessionData = getSessionData()
    if (sessionData.playerName) {
      playerName.value = sessionData.playerName
    }
  }

  // Reset player name
  const resetPlayerName = () => {
    playerName.value = ''
    saveSessionData({ playerName: 'Anonymous Player' })
  }

  return {
    // State
    playerName,
    
    // Computed
    isPlayerNameValid,
    displayName,
    
    // Methods
    generateRandomName,
    updatePlayerName,
    promptForPlayerName,
    ensurePlayerName,
    initializePlayerName,
    resetPlayerName,
  }
}
