<template>
  <div class="price-guess-results-page">
    <div class="max-ctr q-pa-lg">
      <!-- Loading State -->
      <div v-if="isLoading"
           class="loading-container text-center q-pa-xl">
        <q-spinner color="primary"
                   size="3em" />
        <div class="q-mt-md text-h6">Loading Results...</div>
        <div class="text-body2 text-grey-7">Fetching your game results and comparisons</div>
      </div>

      <!-- Error State -->
      <div v-else-if="error"
           class="error-container text-center q-pa-xl">
        <q-icon name="error"
                color="negative"
                size="3em" />
        <div class="q-mt-md text-h6 text-negative">Failed to Load Results</div>
        <div class="text-body2 text-grey-7 q-mb-lg">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               @click="loadResults" />
      </div>

      <!-- Results Content -->
      <div v-else-if="gameResults.length > 0"
           class="results-content">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <div class="performance-badge q-mb-lg">
            <q-icon :name="performanceRating.icon"
                    :color="performanceRating.color"
                    size="4em" />
            <div class="text-h4 text-weight-bold q-mt-md"
                 :class="`text-${performanceRating.color}`">
              {{ performanceRating.rating }}
            </div>
            <div class="text-h6 text-grey-7">{{ totalScore }} / {{ maxPossibleScore }} points</div>
          </div>

          <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">Challenge Complete!</h1>
          <p class="text-body1 text-grey-7">Here's how you performed on each property</p>

          <!-- Session Info -->
          <div class="session-info q-mt-md">
            <!-- <q-chip color="grey-4"
                    text-color="grey-8"
                    icon="fingerprint">
              Session: {{ routeSssnId.substring(0, 8) }}...
            </q-chip>
            <q-chip color="grey-4"
                    text-color="grey-8"
                    icon="schedule">
              {{ formatDate(sessionDate) }}
            </q-chip> -->
          </div>
        </div>

        <!-- Results Table -->
        <q-card class="results-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <q-table :rows="gameResults"
                     :columns="resultsColumns"
                     row-key="id"
                     flat
                     :pagination="{ rowsPerPage: 0 }"
                     class="results-table">
              <template v-slot:body-cell-property="props">
                <q-td :props="props">
                  <div class="property-cell"
                       style="overflow: auto;">
                    <div class="text-weight-medium">{{ props.row.estimate_title }}</div>
                    <div class="text-caption text-grey-6">{{ props.row.estimate_vicinity }}</div>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-guess="props">
                <q-td :props="props">
                  <div class="text-weight-medium">
                    {{ formatPrice(props.row.estimated_price_cents, props.row.estimate_currency) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-actual="props">
                <q-td :props="props">
                  <div class="text-weight-medium">
                    {{ formatPrice(props.row.price_at_time_of_estimate_cents, props.row.estimate_currency) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-difference="props">
                <q-td :props="props">
                  <q-chip :color="Math.abs(props.row.percentage_above_or_below) <= 10 ? 'positive' : Math.abs(props.row.percentage_above_or_below) <= 25 ? 'warning' : 'negative'"
                          text-color="white"
                          size="sm">
                    {{ props.row.percentage_above_or_below > 0 ? '+' : '' }}{{
                      props.row.percentage_above_or_below?.toFixed(1) }}%
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-score="props">
                <q-td :props="props">
                  <div class="score-cell">
                    <q-circular-progress :value="props.row.estimate_details?.game_score || 0"
                                         size="40px"
                                         :thickness="0.15"
                                         :color="getScoreColor(props.row.estimate_details?.game_score || 0)"
                                         track-color="grey-3"
                                         class="q-mr-sm">
                      <div class="text-caption text-weight-bold">{{ props.row.estimate_details?.game_score || 0 }}</div>
                    </q-circular-progress>
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>

        <!-- Comparison Summary -->
        <q-card v-if="singleComparisonData.length > 0"
                class="comparison-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 q-mb-md">
              <q-icon name="people"
                      color="primary"
                      size="sm"
                      class="q-mr-sm" />
              How You Compare to Other Players
            </div>

            <div v-if="isLoadingComparisons"
                 class="text-center q-pa-md">
              <q-spinner color="primary"
                         size="2em" />
              <div class="q-mt-sm text-grey-7">Loading comparison data...</div>
            </div>

            <div v-else>
              <div v-for="(propertyData, index) in singleComparisonData"
                   :key="index"
                   class="property-comparison q-mb-lg">
                <div class="comparison-header q-mb-md">
                  <div class="text-subtitle1 text-weight-medium">{{ propertyData.property_title }}</div>
                  <div class="text-caption text-grey-6">{{ propertyData.property_vicinity }}</div>
                </div>

                <div v-if="propertyData.price_estimates_summary && propertyData.price_estimates_summary.length > 0">
                  <!-- Your guess vs others -->
                  <div class="comparison-stats q-mb-md">
                    <div class="row q-col-gutter-md">
                      <div class="col-12 col-md-4">
                        <q-card flat
                                bordered
                                class="stat-card">
                          <q-card-section class="text-center q-pa-md">
                            <div class="text-h6 text-primary">
                              {{ formatPrice(propertyData.your_guess, propertyData.currency) }}
                            </div>
                            <div class="text-caption text-grey-6">Your Guess</div>
                          </q-card-section>
                        </q-card>
                      </div>
                      <div class="col-12 col-md-4">
                        <q-card flat
                                bordered
                                class="stat-card">
                          <q-card-section class="text-center q-pa-md">
                            <div class="text-h6 text-secondary">
                              {{ formatPrice(propertyData.average_guess, propertyData.currency) }}
                            </div>
                            <div class="text-caption text-grey-6">Average Guess</div>
                          </q-card-section>
                        </q-card>
                      </div>
                      <div class="col-12 col-md-4">
                        <q-card flat
                                bordered
                                class="stat-card">
                          <q-card-section class="text-center q-pa-md">
                            <div class="text-h6 text-positive">
                              {{ formatPrice(propertyData.actual_price, propertyData.currency) }}
                            </div>
                            <div class="text-caption text-grey-6">Actual Price</div>
                          </q-card-section>
                        </q-card>
                      </div>
                    </div>
                  </div>

                  <!-- Performance ranking -->
                  <div class="performance-ranking">
                    <div class="text-subtitle2 q-mb-sm">Your Performance</div>
                    <div class="ranking-info">
                      <q-chip :color="propertyData.ranking_color"
                              text-color="white"
                              icon="emoji_events">
                        Ranked {{ propertyData.ranking }} of {{ propertyData.total_players }}
                      </q-chip>
                      <span class="q-ml-md text-body2 text-grey-7">
                        {{ propertyData.performance_text }}
                      </span>
                    </div>
                  </div>
                </div>

                <div v-else
                     class="text-grey-6 q-pa-md text-center">
                  <q-icon name="info"
                          class="q-mr-sm" />
                  No other estimates available for this property yet
                </div>

                <q-separator v-if="index < comparisonData.length - 1"
                             class="q-mt-lg" />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Actions -->
        <div class="results-actions text-center">
          <!-- <q-btn color="primary"
                 label="Play Again"
                 icon="refresh"
                 size="lg"
                 rounded
                 unelevated
                 @click="playAgain"
                 class="q-mr-md" />
          <q-btn color="secondary"
                 label="Share Results"
                 icon="share"
                 size="lg"
                 rounded
                 outline
                 @click="shareResults" /> -->
        </div>
      </div>

      <!-- No Results -->
      <div v-else
           class="no-results-container text-center q-pa-xl">
        <q-icon name="search_off"
                color="grey-5"
                size="3em" />
        <div class="q-mt-md text-h6 text-grey-7">No Results Found</div>
        <div class="text-body2 text-grey-6 q-mb-lg">
          We couldn't find any results for this game session.
        </div>
        <q-btn color="primary"
               label="Start New Game"
               @click="startNewGame" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { usePriceGuessResults } from '../composables/usePriceGuessResults'

// Props
const props = defineProps({
  routeSssnId: {
    type: String,
    required: true
  }
})

const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()

// Get dossier UUID from route params
// const dossierUuid = computed(() => $route.params.dossierUuid)

// Initialize the results composable
const {
  isLoading,
  error,
  gameResults,
  comparisonData,
  isLoadingComparisons,
  totalScore,
  maxPossibleScore,
  performanceRating,
  sessionDate,
  loadGameResults,
  loadComparisonData,
  formatPrice,
  getScoreColor,
  formatDate
} = usePriceGuessResults()

// Computed properties
const singleComparisonData = computed(() => {
  let guessedUuids = gameResults.value.map(item => item.listing_uuid)
  // console.log(gameResults)
  // filtering below will mean that if the session id has been used to
  // guess other prices, comparisonData for those will be show too
  return comparisonData.value.filter(
    (dataItem) => guessedUuids.includes(dataItem.uuid)
    //  === "dee22a8f-231b-4592-95c2-280def2fbab4"
  )
}
)

const resultsColumns = computed(() => [
  {
    name: 'property',
    label: 'Property',
    field: 'property',
    align: 'left',
    style: 'width: 30%'
  },
  {
    name: 'guess',
    label: 'Your Guess',
    field: 'guess',
    align: 'right',
    style: 'width: 20%'
  },
  {
    name: 'actual',
    label: 'Actual Price',
    field: 'actual',
    align: 'right',
    style: 'width: 20%'
  },
  {
    name: 'difference',
    label: 'Difference',
    field: 'difference',
    align: 'center',
    style: 'width: 15%'
  },
  {
    name: 'score',
    label: 'Score',
    field: 'score',
    align: 'center',
    style: 'width: 15%'
  }
])

// Methods
const loadResults = async () => {
  try {
    await loadGameResults(props.routeSssnId)
    await loadComparisonData(props.routeSssnId)
  } catch (err) {
    console.error('Failed to load results:', err)
  }
}

const playAgain = () => {
  $router.push({
    name: 'rPriceGuessStart',
    params: {
      // // dossierUuid: dossierUuid.value
    }
  })
}

const startNewGame = () => {
  $router.push({
    name: 'rPriceGuessStart',
    params: {
      // // dossierUuid: dossierUuid.value
    }
  })
}

const shareResults = () => {
  const url = window.location.href
  const text = `I just completed the Property Price Challenge! Score: ${totalScore.value}/${maxPossibleScore.value} (${performanceRating.value.rating})`

  if (navigator.share) {
    navigator.share({
      title: 'Property Price Challenge Results',
      text: text,
      url: url
    }).catch(() => {
      fallbackShare(url, text)
    })
  } else {
    fallbackShare(url, text)
  }
}

const fallbackShare = (url, text) => {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(url).then(() => {
      $q.notify({
        message: 'Results URL copied to clipboard!',
        icon: 'content_copy',
        color: 'positive'
      })
    })
  }
}

// Initialize on mount
onMounted(() => {
  loadResults()
})
</script>

<style scoped>
.price-guess-results-page {
  /* background-color: #fafafa; */
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container,
.no-results-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-badge {
  display: inline-block;
  padding: 2rem;
  /* background: #f8f9fa; */
  border-radius: 16px;
  border: 2px solid #e9ecef;
}

.session-info {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.results-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.results-table {
  border-radius: 8px;
}

.property-cell {
  max-width: 200px;
}

.score-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.comparison-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.property-comparison {
  border-left: 4px solid #e0e0e0;
  padding-left: 1rem;
}

.comparison-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.stat-card {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.performance-ranking {
  /* background: #f0f4f8; */
  border-radius: 8px;
  padding: 1rem;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.results-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Button improvements */
.q-btn {
  text-transform: none;
  font-weight: 500;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .max-ctr {
    padding: 1rem;
  }

  .results-header {
    padding: 1rem;
  }

  .session-info {
    flex-direction: column;
    align-items: center;
  }

  .comparison-stats .row {
    flex-direction: column;
  }

  .ranking-info {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>
