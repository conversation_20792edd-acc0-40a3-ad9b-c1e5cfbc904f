/**
 * Example Usage of useRealtyGameStore
 * 
 * This file demonstrates common patterns and usage examples
 * for the Realty Game Store in Vue components.
 */

// Basic import and setup
import { useRealtyGameStore } from 'src/stores/realtyGame'
import { computed, onMounted, onBeforeUnmount } from 'vue'

export default {
  name: 'RealtyGameExample',
  setup() {
    const realtyGameStore = useRealtyGameStore()

    // Example 1: Loading initial game data
    const loadGameData = async () => {
      try {
        // Simulate API call
        const gameData = {
          gameListings: [
            {
              uuid: 'prop-1',
              title: 'Modern London Apartment',
              price_sale_current_cents: 75000000, // £750,000
              city: 'London',
              count_bedrooms: 2
            },
            {
              uuid: 'prop-2', 
              title: 'Victorian House in Bath',
              price_sale_current_cents: 65000000, // £650,000
              city: 'Bath',
              count_bedrooms: 4
            }
          ],
          gameTitle: 'UK Property Price Challenge',
          gameDesc: 'Test your knowledge of UK property prices!',
          gameBgImageUrl: 'https://example.com/uk-properties-bg.jpg'
        }

        realtyGameStore.setRealtyGameData(gameData)
        console.log(`Loaded ${realtyGameStore.getTotalProperties} properties`)
      } catch (error) {
        console.error('Failed to load game data:', error)
      }
    }

    // Example 2: Computed properties for reactive UI
    const gameStatus = computed(() => ({
      isLoaded: realtyGameStore.getIsDataLoaded,
      totalProperties: realtyGameStore.getTotalProperties,
      hasProperties: realtyGameStore.getTotalProperties > 0,
      firstProperty: realtyGameStore.firstVisibleListing
    }))

    // Example 3: Property navigation functions
    const navigateToProperty = (uuid) => {
      const property = realtyGameStore.getPropertyByUuid(uuid)
      if (property) {
        realtyGameStore.setRealtyGameData({
          ...realtyGameStore.$state,
          currentProperty: property
        })
        return property
      }
      console.warn(`Property with UUID ${uuid} not found`)
      return null
    }

    const navigateByIndex = (index) => {
      const property = realtyGameStore.getPropertyByIndex(index)
      if (property) {
        realtyGameStore.setRealtyGameData({
          ...realtyGameStore.$state,
          currentProperty: property
        })
        return property
      }
      console.warn(`No property at index ${index}`)
      return null
    }

    // Example 4: Property search and filtering
    const searchProperties = (searchTerm) => {
      const properties = realtyGameStore.getGameListings
      return properties.filter(property => 
        property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.city.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    const getPropertiesByCity = (city) => {
      return realtyGameStore.getGameListings.filter(
        property => property.city.toLowerCase() === city.toLowerCase()
      )
    }

    // Example 5: Game state management
    const resetGame = () => {
      realtyGameStore.clearRealtyGameData()
      console.log('Game data cleared')
    }

    const isGameReady = computed(() => {
      return realtyGameStore.getIsDataLoaded && 
             realtyGameStore.getTotalProperties > 0
    })

    // Example 6: Lifecycle management
    onMounted(async () => {
      if (!realtyGameStore.getIsDataLoaded) {
        await loadGameData()
      }
    })

    onBeforeUnmount(() => {
      // Optional: Clear data when component unmounts
      // realtyGameStore.clearRealtyGameData()
    })

    // Example 7: Watchers for store changes
    const currentProperty = computed(() => realtyGameStore.getCurrentProperty)
    
    // Example 8: Utility functions
    const getPropertyPrice = (property) => {
      if (!property || !property.price_sale_current_cents) return 'Price not available'
      const pounds = property.price_sale_current_cents / 100
      return new Intl.NumberFormat('en-GB', {
        style: 'currency',
        currency: property.currency || 'GBP'
      }).format(pounds)
    }

    const getPropertySummary = (uuid) => {
      const property = realtyGameStore.getPropertyByUuid(uuid)
      if (!property) return null

      return {
        title: property.title,
        location: `${property.city}, ${property.country}`,
        price: getPropertyPrice(property),
        bedrooms: property.count_bedrooms,
        summary: `${property.count_bedrooms} bed property in ${property.city}`
      }
    }

    // Return reactive properties and methods for template
    return {
      // Store access
      realtyGameStore,
      
      // Computed properties
      gameStatus,
      isGameReady,
      currentProperty,
      
      // Methods
      loadGameData,
      navigateToProperty,
      navigateByIndex,
      searchProperties,
      getPropertiesByCity,
      resetGame,
      getPropertyPrice,
      getPropertySummary
    }
  }
}

// Example template usage:
/*
<template>
  <div class="realty-game-container">
    <!-- Game header -->
    <div v-if="gameStatus.isLoaded" class="game-header">
      <h1>{{ realtyGameStore.getGameTitle }}</h1>
      <p>{{ realtyGameStore.getGameDesc }}</p>
      <div 
        v-if="realtyGameStore.getGameBgImageUrl"
        class="bg-image"
        :style="{ backgroundImage: `url(${realtyGameStore.getGameBgImageUrl})` }"
      />
    </div>

    <!-- Loading state -->
    <div v-else class="loading">
      <p>Loading game data...</p>
      <button @click="loadGameData">Retry</button>
    </div>

    <!-- Game content -->
    <div v-if="isGameReady" class="game-content">
      <div class="stats">
        <p>Properties loaded: {{ gameStatus.totalProperties }}</p>
        <p v-if="currentProperty">Current: {{ currentProperty.title }}</p>
      </div>

      <!-- Property list -->
      <div class="property-list">
        <div 
          v-for="(property, index) in realtyGameStore.getGameListings"
          :key="property.uuid"
          class="property-card"
          :class="{ active: currentProperty?.uuid === property.uuid }"
          @click="navigateToProperty(property.uuid)"
        >
          <h3>{{ property.title }}</h3>
          <p>{{ getPropertyPrice(property) }}</p>
          <p>{{ property.city }} • {{ property.count_bedrooms }} bedrooms</p>
        </div>
      </div>

      <!-- Navigation -->
      <div class="navigation">
        <button 
          v-for="n in gameStatus.totalProperties"
          :key="n"
          @click="navigateByIndex(n - 1)"
          :disabled="currentProperty?.uuid === realtyGameStore.getPropertyByIndex(n - 1)?.uuid"
        >
          {{ n }}
        </button>
      </div>
    </div>

    <!-- Game controls -->
    <div class="controls">
      <button @click="resetGame" :disabled="!gameStatus.isLoaded">
        Reset Game
      </button>
    </div>
  </div>
</template>
*/
