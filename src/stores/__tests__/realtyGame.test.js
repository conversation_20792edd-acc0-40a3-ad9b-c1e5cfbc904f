import { describe, it, expect, beforeEach } from 'vitest'
import { setActive<PERSON><PERSON>, createPinia } from 'pinia'
import { useRealtyGameStore } from '../realtyGame'
import { createMockProperty } from 'src/test/utils.js'

describe('useRealtyGameStore', () => {
  let store

  beforeEach(() => {
    // Setup a fresh Pinia instance for each test
    setActivePinia(createPinia())
    store = useRealtyGameStore()
  })

  describe('initial state', () => {
    it('should have correct initial state', () => {
      expect(store.gameListings).toEqual([])
      expect(store.gameTitle).toBe('Property Price Challenge')
      expect(store.gameDesc).toBe('')
      expect(store.gameBgImageUrl).toBe('')
      expect(store.currentProperty).toBeNull()
      expect(store.isDataLoaded).toBe(false)
    })
  })

  describe('actions', () => {
    describe('setRealtyGameData', () => {
      it('should set all game data when provided', () => {
        const mockProperty1 = createMockProperty({ uuid: 'prop-1', title: 'Property 1' })
        const mockProperty2 = createMockProperty({ uuid: 'prop-2', title: 'Property 2' })
        
        const gameData = {
          gameListings: [mockProperty1, mockProperty2],
          gameTitle: 'Custom Game Title',
          gameDesc: 'Custom game description',
          gameBgImageUrl: 'https://example.com/bg-image.jpg',
          currentProperty: mockProperty1
        }

        store.setRealtyGameData(gameData)

        expect(store.gameListings).toEqual([mockProperty1, mockProperty2])
        expect(store.gameTitle).toBe('Custom Game Title')
        expect(store.gameDesc).toBe('Custom game description')
        expect(store.gameBgImageUrl).toBe('https://example.com/bg-image.jpg')
        expect(store.currentProperty).toEqual(mockProperty1)
        expect(store.isDataLoaded).toBe(true)
      })

      it('should use default values when properties are missing', () => {
        const gameData = {
          gameListings: []
        }

        store.setRealtyGameData(gameData)

        expect(store.gameListings).toEqual([])
        expect(store.gameTitle).toBe('Property Price Challenge')
        expect(store.gameDesc).toBe('')
        expect(store.gameBgImageUrl).toBe('')
        expect(store.currentProperty).toBeNull()
        expect(store.isDataLoaded).toBe(true)
      })

      it('should handle empty data object', () => {
        store.setRealtyGameData({})

        expect(store.gameListings).toEqual([])
        expect(store.gameTitle).toBe('Property Price Challenge')
        expect(store.gameDesc).toBe('')
        expect(store.gameBgImageUrl).toBe('')
        expect(store.currentProperty).toBeNull()
        expect(store.isDataLoaded).toBe(true)
      })
    })

    describe('clearRealtyGameData', () => {
      it('should reset all data to initial state', () => {
        // First set some data
        const mockProperty = createMockProperty()
        store.setRealtyGameData({
          gameListings: [mockProperty],
          gameTitle: 'Test Game',
          gameDesc: 'Test Description',
          gameBgImageUrl: 'https://example.com/test.jpg',
          currentProperty: mockProperty
        })

        // Verify data was set
        expect(store.isDataLoaded).toBe(true)
        expect(store.gameListings.length).toBe(1)

        // Clear the data
        store.clearRealtyGameData()

        // Verify all data is reset
        expect(store.gameListings).toEqual([])
        expect(store.gameTitle).toBe('Property Price Challenge')
        expect(store.gameDesc).toBe('')
        expect(store.gameBgImageUrl).toBe('')
        expect(store.currentProperty).toBeNull()
        expect(store.isDataLoaded).toBe(false)
      })
    })

    describe('getPropertyByIndex', () => {
      beforeEach(() => {
        const mockProperties = [
          createMockProperty({ uuid: 'prop-1', title: 'Property 1' }),
          createMockProperty({ uuid: 'prop-2', title: 'Property 2' }),
          createMockProperty({ uuid: 'prop-3', title: 'Property 3' })
        ]
        
        store.setRealtyGameData({ gameListings: mockProperties })
      })

      it('should return property at valid index', () => {
        const property = store.getPropertyByIndex(0)
        expect(property).toBeTruthy()
        expect(property.uuid).toBe('prop-1')
        expect(property.title).toBe('Property 1')
      })

      it('should return property at middle index', () => {
        const property = store.getPropertyByIndex(1)
        expect(property).toBeTruthy()
        expect(property.uuid).toBe('prop-2')
      })

      it('should return null for invalid index', () => {
        expect(store.getPropertyByIndex(-1)).toBeNull()
        expect(store.getPropertyByIndex(999)).toBeNull()
        expect(store.getPropertyByIndex(3)).toBeNull() // Out of bounds
      })

      it('should return null when no properties exist', () => {
        store.clearRealtyGameData()
        expect(store.getPropertyByIndex(0)).toBeNull()
      })
    })

    describe('getPropertyByUuid', () => {
      beforeEach(() => {
        const mockProperties = [
          createMockProperty({ uuid: 'prop-1', title: 'Property 1' }),
          createMockProperty({ uuid: 'prop-2', title: 'Property 2' }),
          createMockProperty({ uuid: 'prop-3', title: 'Property 3' })
        ]
        
        store.setRealtyGameData({ gameListings: mockProperties })
      })

      it('should return property with matching uuid', () => {
        const property = store.getPropertyByUuid('prop-2')
        expect(property).toBeTruthy()
        expect(property.uuid).toBe('prop-2')
        expect(property.title).toBe('Property 2')
      })

      it('should return null for non-existent uuid', () => {
        expect(store.getPropertyByUuid('non-existent')).toBeNull()
      })

      it('should return null for undefined uuid', () => {
        expect(store.getPropertyByUuid(undefined)).toBeNull()
      })

      it('should return null for null uuid', () => {
        expect(store.getPropertyByUuid(null)).toBeNull()
      })

      it('should return null when no properties exist', () => {
        store.clearRealtyGameData()
        expect(store.getPropertyByUuid('prop-1')).toBeNull()
      })
    })
  })

  describe('getters', () => {
    beforeEach(() => {
      const mockProperties = [
        createMockProperty({ uuid: 'prop-1', title: 'Property 1' }),
        createMockProperty({ uuid: 'prop-2', title: 'Property 2' })
      ]
      
      const gameData = {
        gameListings: mockProperties,
        gameTitle: 'Test Game Title',
        gameDesc: 'Test game description',
        gameBgImageUrl: 'https://example.com/bg.jpg',
        currentProperty: mockProperties[0]
      }
      
      store.setRealtyGameData(gameData)
    })

    it('getGameListings should return game listings', () => {
      expect(store.getGameListings).toHaveLength(2)
      expect(store.getGameListings[0].uuid).toBe('prop-1')
    })

    it('getGameTitle should return game title', () => {
      expect(store.getGameTitle).toBe('Test Game Title')
    })

    it('getGameDesc should return game description', () => {
      expect(store.getGameDesc).toBe('Test game description')
    })

    it('getGameBgImageUrl should return background image URL', () => {
      expect(store.getGameBgImageUrl).toBe('https://example.com/bg.jpg')
    })

    it('getCurrentProperty should return current property', () => {
      expect(store.getCurrentProperty).toBeTruthy()
      expect(store.getCurrentProperty.uuid).toBe('prop-1')
    })

    it('getTotalProperties should return total number of properties', () => {
      expect(store.getTotalProperties).toBe(2)
    })

    it('getIsDataLoaded should return data loaded status', () => {
      expect(store.getIsDataLoaded).toBe(true)
    })

    it('firstVisibleListing should return first property', () => {
      expect(store.firstVisibleListing).toBeTruthy()
      expect(store.firstVisibleListing.uuid).toBe('prop-1')
    })

    it('firstVisibleListing should return null when no properties', () => {
      store.clearRealtyGameData()
      expect(store.firstVisibleListing).toBeNull()
    })
  })

  describe('edge cases and data consistency', () => {
    it('should handle large datasets', () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => 
        createMockProperty({ 
          uuid: `prop-${i}`, 
          title: `Property ${i}` 
        })
      )

      store.setRealtyGameData({ gameListings: largeDataset })

      expect(store.getTotalProperties).toBe(1000)
      expect(store.getPropertyByIndex(999)).toBeTruthy()
      expect(store.getPropertyByUuid('prop-500')).toBeTruthy()
    })

    it('should maintain data integrity across multiple operations', () => {
      // Initial setup
      const initialProperty = createMockProperty({ uuid: 'initial', title: 'Initial' })
      store.setRealtyGameData({
        gameListings: [initialProperty],
        gameTitle: 'Initial Title'
      })

      expect(store.getTotalProperties).toBe(1)
      expect(store.getGameTitle).toBe('Initial Title')

      // Update with new data
      const updatedProperties = [
        createMockProperty({ uuid: 'updated-1', title: 'Updated 1' }),
        createMockProperty({ uuid: 'updated-2', title: 'Updated 2' })
      ]
      
      store.setRealtyGameData({
        gameListings: updatedProperties,
        gameTitle: 'Updated Title'
      })

      expect(store.getTotalProperties).toBe(2)
      expect(store.getGameTitle).toBe('Updated Title')
      expect(store.getPropertyByUuid('initial')).toBeNull() // Old property should be gone
      expect(store.getPropertyByUuid('updated-1')).toBeTruthy()
    })

    it('should handle special characters in titles and descriptions', () => {
      const specialCharsData = {
        gameTitle: 'Game with Special Characters: éñ中文🏠',
        gameDesc: 'Description with <HTML> & "quotes" and émojis 🎮',
        gameListings: [
          createMockProperty({ 
            title: 'Property with Special Chars: éñ中文🏠',
            description: 'Description with <HTML> & "quotes"'
          })
        ]
      }

      store.setRealtyGameData(specialCharsData)

      expect(store.getGameTitle).toBe('Game with Special Characters: éñ中文🏠')
      expect(store.getGameDesc).toBe('Description with <HTML> & "quotes" and émojis 🎮')
      expect(store.getGameListings[0].title).toBe('Property with Special Chars: éñ中文🏠')
    })
  })
})
