import { defineStore } from 'pinia'

/**
 * Pinia store for managing the Realty Game state
 * 
 * This store handles:
 * - Property listings for the game
 * - Game metadata (title, description, background image)
 * - Current property tracking
 * - Data loading state
 * 
 * @see README-realtyGame.md for detailed documentation
 */
export const useRealtyGameStore = defineStore('realtyGame', {
  state: () => ({
    /** @type {Array} Array of property objects for the game */
    gameListings: [],
    /** @type {string} Display title for the game */
    gameTitle: 'Property Price Challenge',
    /** @type {string} Description text for the game */
    gameDesc: '',
    /** @type {string} URL for the game's background image */
    gameBgImageUrl: '',
    /** @type {Object|null} Currently selected/viewed property */
    currentProperty: null,
    /** @type {boolean} Flag indicating if game data has been loaded */
    isDataLoaded: false,
  }),
  actions: {
    /**
     * Sets the complete game data, replacing all current state
     * @param {Object} data - Game data object
     * @param {Array} [data.gameListings=[]] - Array of property objects
     * @param {string} [data.gameTitle='Property Price Challenge'] - Game display title
     * @param {string} [data.gameDesc=''] - Game description
     * @param {string} [data.gameBgImageUrl=''] - Background image URL
     * @param {Object} [data.currentProperty=null] - Currently selected property
     */
    setRealtyGameData(data) {
      this.gameListings = data.gameListings || []
      this.gameTitle = data.gameTitle || 'Property Price Challenge'
      this.gameDesc = data.gameDesc || ''
      this.gameBgImageUrl = data.gameBgImageUrl || ''
      this.currentProperty = data.currentProperty || null
      this.isDataLoaded = true
    },
    /**
     * Resets all game data to initial state
     */
    clearRealtyGameData() {
      this.gameListings = []
      this.gameTitle = 'Property Price Challenge'
      this.gameDesc = ''
      this.gameBgImageUrl = ''
      this.currentProperty = null
      this.isDataLoaded = false
    },
    /**
     * Retrieves a property by its array index
     * @param {number} index - Zero-based index of the property
     * @returns {Object|null} Property object if found, null otherwise
     */
    getPropertyByIndex(index) {
      return this.gameListings[index] || null
    },
    /**
     * Retrieves a property by its unique identifier
     * @param {string} uuid - Unique identifier of the property
     * @returns {Object|null} Property object if found, null otherwise
     */
    getPropertyByUuid(uuid) {
      return this.gameListings.find((prop) => prop.uuid === uuid) || null
    },
  },
  getters: {
    /** @returns {Array} Array of game property listings */
    getGameListings: (state) => state.gameListings,
    /** @returns {string} Current game title */
    getGameTitle: (state) => state.gameTitle,
    /** @returns {string} Current game description */
    getGameDesc: (state) => state.gameDesc,
    /** @returns {string} Background image URL */
    getGameBgImageUrl: (state) => state.gameBgImageUrl,
    /** @returns {Object|null} Currently selected property */
    getCurrentProperty: (state) => state.currentProperty,
    /** @returns {number} Total number of properties in the game */
    getTotalProperties: (state) => state.gameListings.length,
    /** @returns {boolean} Whether game data has been loaded */
    getIsDataLoaded: (state) => state.isDataLoaded,
    /** @returns {Object|null} First property in the listings array, or null if empty */
    firstVisibleListing: (state) => state.gameListings[0] || null,
  },
})
