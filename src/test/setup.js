import { config } from '@vue/test-utils'
import { createPinia } from 'pinia'

// Mock global window properties
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    reload: vi.fn()
  },
  writable: true
})

// Mock navigator
Object.defineProperty(navigator, 'share', {
  value: vi.fn(),
  writable: true
})

// Setup global Vue Test Utils config with comprehensive stubs
config.global.stubs = {
  'q-no-ssr': {
    template: '<div><slot /></div>'
  },
  'q-spinner': {
    template: '<div data-testid="loading-spinner">Loading...</div>'
  },
  'q-icon': {
    template: '<div data-testid="icon">Icon</div>'
  },
  'q-btn': {
    template: '<button v-bind="buttonProps" @click="handleClick"><slot /></button>',
    props: ['label', 'disable', 'color', 'icon', 'size', 'unelevated', 'rounded', 'loading', 'class', 'type'],
    emits: ['click'],
    computed: {
      buttonProps() {
        const { disable, ...otherProps } = this.$props
        return {
          ...otherProps,
          disabled: disable
        }
      }
    },
    methods: {
      handleClick(event) {
        if (!this.disable && !this.loading) {
          this.$emit('click', event)
        }
      },
      trigger(event) {
        if (event === 'click') {
          this.$emit('click')
        }
      }
    }
  },
  'q-card': {
    template: '<div class="q-card" v-bind="$props"><slot /></div>',
    props: ['flat', 'bordered']
  },
  'q-card-section': {
    template: '<div class="q-card-section" v-bind="$props"><slot /></div>',
    props: ['class']
  },
  'q-card-actions': {
    template: '<div class="q-card-actions"><slot /></div>'
  },
  'q-input': {
    template: '<div class="q-input"><span v-if="prefix" class="q-input-prefix">{{ prefix }}</span><input v-bind="inputProps" data-testid="input" @input="$emit(\'update:modelValue\', $event.target.value)" /></div>',
    props: ['label', 'modelValue', 'type', 'outlined', 'dense', 'prefix', 'placeholder', 'error', 'errorMessage'],
    emits: ['update:modelValue', 'keyup'],
    computed: {
      inputProps() {
        const { prefix, ...otherProps } = this.$props
        return otherProps
      }
    },
    methods: {
      setValue(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  'q-carousel': {
    template: '<div class="q-carousel" v-bind="$props"><slot /></div>',
    props: ['modelValue', 'animated', 'swipeable', 'thumbnails', 'arrows', 'infinite', 'height', 'controlColor', 'controlType']
  },
  'q-carousel-slide': {
    template: '<div class="q-carousel-slide" v-bind="$props"><slot /></div>',
    props: ['name', 'imgSrc']
  },
  'q-dialog': {
    template: '<div v-if="modelValue" class="q-dialog" v-bind="$props"><slot /></div>',
    props: ['modelValue'],
    emits: ['update:modelValue']
  },
  'q-avatar': {
    template: '<div class="q-avatar"><slot /></div>'
  },
  'q-linear-progress': {
    template: '<div class="q-linear-progress"></div>',
    props: ['value', 'color', 'size', 'rounded']
  },
  'q-banner': {
    template: '<div class="q-banner" v-bind="$props"><slot /></div>',
    props: ['class']
  },
  'q-tooltip': {
    template: '<div class="q-tooltip"><slot /></div>'
  },
  'q-separator': {
    template: '<hr class="q-separator" />'
  },
  'q-form': {
    template: '<form class="q-form"><slot /></form>'
  },
  'q-expansion-item': {
    template: '<div class="q-expansion-item"><slot /></div>'
  },
  'PropertyJsonLd': {
    template: '<div data-testid="property-json-ld" style="display: none;"></div>',
    props: ['property', 'breadcrumbs']
  },
  'router-link': {
    template: '<a><slot /></a>'
  },
  'transition': {
    template: '<div><slot /></div>'
  }
}

config.global.plugins = [
  createPinia()
]

// Mock axios
vi.mock('axios', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    create: vi.fn(() => ({
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    }))
  }
}))

// Mock Vue Router
vi.mock('vue-router', () => ({
  useRoute: vi.fn(() => ({
    params: {},
    query: {},
    path: '/test',
    matched: []
  })),
  useRouter: vi.fn(() => ({
    push: vi.fn(),
    go: vi.fn(),
    replace: vi.fn()
  }))
}))

// Mock Quasar composables
vi.mock('quasar', async () => {
  const actual = await vi.importActual('quasar')
  return {
    ...actual,
    useQuasar: vi.fn(() => ({
      notify: vi.fn(),
      dialog: vi.fn(),
      loading: {
        show: vi.fn(),
        hide: vi.fn()
      }
    }))
  }
})

// Mock PWB Config
vi.mock('boot/pwb-flex-conf', () => ({
  pwbFlexConfig: {
    dataApiBase: 'https://api.test.com'
  }
}))

// Mock JSON-LD composable
vi.mock('src/compose/useJsonLd.js', () => ({
  default: vi.fn(() => ({
    addJsonLd: vi.fn(),
    removeJsonLd: vi.fn()
  }))
}))

// Mock Leaflet components
vi.mock('src/concerns/price-guess/components/PriceGuessLeafletMap.vue', () => ({
  default: {
    name: 'PriceGuessLeafletMap',
    template: '<div data-testid="price-guess-leaflet-map">Mock Map</div>'
  }
}))

vi.mock('src/concerns/price-guess/components/BoundaryLeafletMap.vue', () => ({
  default: {
    name: 'BoundaryLeafletMap',
    template: '<div data-testid="boundary-leaflet-map">Mock Boundary Map</div>'
  }
}))

vi.mock('src/components/seo/PropertyJsonLd.vue', () => ({
  default: {
    name: 'PropertyJsonLd',
    template: '<script type="application/ld+json" data-testid="property-json-ld"></script>'
  }
}))
