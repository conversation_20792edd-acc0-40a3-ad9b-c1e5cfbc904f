import { createP<PERSON>, setActiveP<PERSON> } from 'pinia'
import { ref } from 'vue'

// Mock data factories
export const createMockProperty = (overrides = {}) => ({
  uuid: 'test-property-uuid',
  title: 'Beautiful Test Property',
  description: 'A lovely property for testing',
  city: 'Test City',
  postal_code: 'TEST123',
  region: 'Test Region',
  country: 'Test Country',
  street_address: '123 Test Street',
  price_sale_current_cents: 50000000, // £500,000
  currency: 'GBP',
  count_bedrooms: 3,
  count_bathrooms: 2,
  count_garages: 1,
  latitude: 51.5074,
  longitude: -0.1278,
  photos: [
    {
      image_details: {
        url: 'https://example.com/image1.jpg'
      },
      flag_is_hidden: false,
      photo_title: 'Front View'
    },
    {
      image_details: {
        url: 'https://example.com/image2.jpg'
      },
      flag_is_hidden: false,
      photo_title: 'Back Garden'
    }
  ],
  ...overrides
})

export const createMockGameData = (overrides = {}) => ({
  gameListings: [
    {
      listing_details: createMockProperty(),
      uuid: 'test-property-uuid'
    }
  ],
  gameTitle: 'Test Property Game',
  gameDesc: 'Test your property knowledge',
  gameBgImageUrl: 'https://example.com/bg.jpg',
  ...overrides
})

export const createMockGuessData = (overrides = {}) => ({
  guess: 450000,
  score: 85,
  difference: -10.0,
  feedback: 'Great guess!',
  actualPrice: 500000,
  propertyUuid: 'test-property-uuid',
  propertyTitle: 'Beautiful Test Property',
  currency: 'GBP',
  playerName: 'Test Player',
  submittedAt: '2025-07-21T10:00:00.000Z',
  sessionId: 'test-session-id',
  ...overrides
})

// Mock composable factories
export const createMockRealtyGame = (overrides = {}) => ({
  isLoading: ref(false),
  error: ref(null),
  gameListings: ref([createMockProperty()]),
  totalProperties: ref(1),
  gameTitle: ref('Test Game'),
  gameDesc: ref('Test Description'),
  gameBgImageUrl: ref('https://example.com/bg.jpg'),
  getPropertyByIndex: vi.fn(() => ({ listing_details: createMockProperty() })),
  getPropertyByUuid: vi.fn(() => createMockProperty()),
  getPropertyImages: vi.fn((property) => property?.photos || createMockProperty().photos),
  validateGuess: vi.fn(() => ({ isValid: true, errors: [] })),
  submitGameGuess: vi.fn(() => Promise.resolve({
    success: true,
    result: {
      score: 85,
      difference: -10.0,
      feedback: 'Great guess!',
      actualPrice: 500000,
      savedEstimate: {
        guessed_price: {
          uuid: 'guess-uuid',
          guessed_price_in_ui_currency_cents: 45000000
        }
      }
    }
  })),
  getScoreColor: vi.fn(() => 'positive'),
  fetchPriceGuessData: vi.fn(() => Promise.resolve()),
  fetchPropertyByUuid: vi.fn(() => Promise.resolve({
    sale_listing: createMockProperty()
  })),
  setRealtyGameData: vi.fn(),
  ...overrides
})

export const createMockRealtyGameStorage = (overrides = {}) => ({
  getSessionData: vi.fn(() => ({ playerName: 'Test Player' })),
  saveGuess: vi.fn(),
  getGuess: vi.fn(() => null),
  hasGuessed: vi.fn(() => false),
  getCurrencySelection: vi.fn(() => 'GBP'),
  ...overrides
})

export const createMockCurrencyConverter = (overrides = {}) => ({
  selectedCurrency: ref('GBP'),
  setCurrency: vi.fn(),
  formatPriceWithBothCurrencies: vi.fn((price) => `£${(price / 100).toLocaleString()}`),
  getCurrencySymbol: vi.fn(() => '£'),
  convertPrice: vi.fn((price) => price),
  ...overrides
})

export const createMockStore = () => {
  const pinia = createPinia()
  setActivePinia(pinia)
  
  const mockStore = {
    currentProperty: createMockProperty(),
    gameListings: [createMockProperty()],
    gameTitle: 'Test Game',
    gameDesc: 'Test Description',
    gameBgImageUrl: 'https://example.com/bg.jpg',
    isDataLoaded: true,
    setRealtyGameData: vi.fn()
  }
  
  return mockStore
}

// Test helpers
export const flushPromises = () => new Promise(resolve => setTimeout(resolve, 0))

export const nextTick = () => new Promise(resolve => setTimeout(resolve, 0))
