# Session ID Fix Testing Guide

## Problem Summary
The `currPlayerSssnId` prop was sometimes missing when `handleSubmitRupGameGuess` was called, causing issues with game guess submissions.

## Root Cause Analysis
1. **Layout Dependency**: `currPlayerSssnId` in `RoundupGamePagesLayout.vue` depends on `getCurrentSessionId()` from the storage composable
2. **Storage Initialization**: The storage composable initializes from localStorage, but this can fail or be empty
3. **SSR Context**: During server-side rendering, localStorage is not available
4. **Race Conditions**: The session ID might not be initialized when the component first renders

## Circumstances When currPlayerSssnId is Missing
1. **First-time users**: No existing session ID in localStorage
2. **Cleared browser data**: localStorage was cleared
3. **SSR rendering**: Server-side rendering context where localStorage is unavailable
4. **Storage initialization failure**: Corrupted localStorage data causing initialization to fail
5. **Race conditions**: Component renders before storage is fully initialized

## Implemented Solutions

### 1. Enhanced Property Page (`RoundupGamePropertyPage.vue`)
- **Session Validation**: Check if `currPlayerSssnId` exists before submission
- **User Dialog**: Show informative dialog when session creation is needed
- **Fallback Creation**: Use `getOrCreateSessionId()` as fallback
- **Final Validation**: Ensure session ID exists before API call
- **Consistent Usage**: Use the same session ID for submission and storage

### 2. Improved Layout (`RoundupGamePagesLayout.vue`)
- **Storage Initialization**: Explicitly call `initializeFromStorage()` in `onMounted`
- **Better Error Handling**: Log warnings when session ID is missing
- **Defensive Programming**: Don't create session in computed property to avoid reactivity issues

### 3. Robust Navigation
- **Session Fallback**: Use fallback session ID in `handleNextProperty`
- **Consistent Routing**: Ensure navigation always has a valid session ID

## Testing Scenarios

### Test 1: First-time User
1. Clear browser localStorage
2. Navigate to game property page
3. Try to submit a guess
4. **Expected**: Dialog appears, session is created, guess submits successfully

### Test 2: Corrupted Storage
1. Set invalid data in localStorage for session keys
2. Navigate to game property page
3. Try to submit a guess
4. **Expected**: Storage is cleared and new session created

### Test 3: SSR Context
1. Disable JavaScript temporarily
2. Navigate to game property page
3. Re-enable JavaScript
4. Try to submit a guess
5. **Expected**: Session is created on client-side, guess works

### Test 4: Existing Session
1. Complete a game normally (creating a session)
2. Navigate to another property
3. Try to submit a guess
4. **Expected**: Uses existing session, no dialog shown

## Code Changes Summary

### Key Functions Modified:
- `handleSubmitRupGameGuess()`: Added session validation and creation
- `handleNextProperty()`: Added session fallback for navigation
- Layout `onMounted()`: Added explicit storage initialization
- Layout `currPlayerSssnId` computed: Added warning logging

### New User Experience:
- Clear messaging when session creation is needed
- Non-blocking dialog that explains the purpose
- Graceful fallback without losing user's guess
- Consistent session handling across navigation

## Monitoring Points
- Check browser console for session-related warnings
- Monitor user feedback about unexpected dialogs
- Track session creation success rates
- Watch for any remaining submission failures

## Future Improvements
1. **Proactive Session Creation**: Create session earlier in the user journey
2. **Session Persistence**: Consider server-side session management
3. **Better Error Recovery**: More sophisticated error handling for edge cases
4. **User Feedback**: Collect analytics on session creation patterns
