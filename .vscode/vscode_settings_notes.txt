11 july 2025:

Frustrated with how I've got a pop-up saying there wasn't a default editor for that vue files and got a suggestion to use below: 

"editor.defaultFormatter": "Wscats.vue"

Reverted to:
  "editor.defaultFormatter": "esbenp.prettier-vscode",

{
  "editor.bracketPairColorization.enabled": true,
  "editor.guides.bracketPairs": true,
  "editor.formatOnSave": false,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": [
    "source.fixAll.eslint"
  ],
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "vue"
  ],
  "[shellscript]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[dotenv]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[vue]": {
    "editor.defaultFormatter": "Wscats.vue"
  }
}